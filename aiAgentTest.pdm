<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ID="{8761C770-F1FE-4E6F-AAD2-1D6BC1821445}" Label="" LastModificationDate="1750298160" Name="aiAgentTest" Objects="642" Symbols="40" Target="MySQL 5.0" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="16.6.4.5517"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<a:SessionID>00000000-0000-0000-0000-000000000000</a:SessionID>
<c:Children>
<o:Model Id="o2">
<a:ObjectID>8761C770-F1FE-4E6F-AAD2-1D6BC1821445</a:ObjectID>
<a:Name>aiAgentTest</a:Name>
<a:Code>aiAgentTest</a:Code>
<a:CreationDate>1746529262</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750298115</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {********-2EB4-4CDC-B4E1-6489961808EC}
DAT 1750296243
ATT FOPT
DLD {5CB45D90-A314-447B-9D0A-C4A1705FD8DB}
DLD {FB3EA266-ACC1-4184-80F6-A891292D7F77}
DLD {EEEAB3E1-47B4-4AC7-8FC8-E033B8D7B6E7}
DLD {A2E54EC2-5EB6-4A92-9BDA-478394A489B1}
DLD {3F952F48-92B7-4FD3-B9FE-F57F69B3E227}
DLD {9CCB9802-651F-4ED9-85A5-284AD245EDD2}
DLD {D7D90F5C-95BE-4A45-8EC4-36970F574E34}
DLD {EEE87F64-2810-493F-AFF3-3A8179C55B93}
DLD {CDEA3FB7-1A68-4271-A154-E775BF8580B0}
ATT NAME
ATT DISPNAME
ATT CODE
DLD {604C93BE-A5E4-4D53-8C8F-4B5B4B8E9F7E}
DLD {1DF46AE6-F61C-44CA-A342-8CE091C661F8}
DLD {02B8BD32-AD53-48E9-B46D-D3788B835657}
DLD {4CAE5A31-E8D6-4F4E-89BA-839E9B2DF6D1}
DLD {55069F62-1493-4660-9D30-DFD0EB61E1D9}
DLD {09A7E9B3-8B49-403D-97E5-65300E940A45}
DLD {46A3D2ED-DDB7-4B63-9B01-0F5DE662633D}
DLD {F2AB20E3-DF76-4376-8C6F-23D1587E1BEE}
DLD {7A89B311-EB45-4EC0-8ACC-29AB6B7649A4}
DLD {48E06F73-5083-4CC6-8478-970B6BBED972}
DLD {F236A522-5B28-40A0-8E31-1C1F20B361BE}
DLD {EE715805-B028-4575-AAEB-16D435086082}
DLD {457B864A-A79A-4BAF-A053-76BB1C4DF339}
DLD {0B12EFB0-E250-4998-BAAA-7778F039A37E}
DLD {9AC07E6F-5774-4AFC-A0D9-407B72651A8B}
DLD {D1BE31DD-CD2A-4221-8A0C-F979B4937C7F}
DLD {F3013BAB-0E2C-4D65-9E27-F498D31FB405}
DLD {A059AF97-1151-4F00-8F6D-9348466D365F}
DLD {79040365-2089-4C9C-9E58-7ED9549F71F1}
DLD {5092A696-17A7-4105-A389-0F465E2A68F8}
DLD {F81C5002-AB6A-4DDD-8FD4-4673E0E75E9F}
DLD {24B90627-AEAB-43EE-B04F-DBAB46BDE92C}
DLD {BBE20597-DDF9-4105-BDFC-30A7FBC1682B}
DLD {6D6ECF90-1B30-44E5-95B0-A3F305929493}
DLD {11BDCB1A-FC90-4DAF-8BEC-3342BC3C341E}
DLD {03024C3F-56CA-4FEE-8239-DE3F2C98E6F0}
DLD {4E8401A7-8E47-4AC3-AB7D-65C32E3415CA}
DLD {D2AB851E-C3B6-4545-A82D-C0FABADD4C2E}
DLD {128D7877-32EE-42CE-B59F-05C12812C211}
DLD {834A3564-9685-405A-B415-CDEAEF8651B4}
DLD {7B443DDF-586C-41D3-9C6D-C084511C7FD4}
DLD {1584D8C3-0551-4DC5-BDD5-DDF5706BF555}
DLD {34A570FE-FBC9-4CCB-BF10-6FDC9C82B2F6}
DLD {A0A8400F-8359-429A-85FD-0D7D7A7CA5EB}
DLD {3AD6917A-2213-4F25-94BD-6975EBAF57A7}
DLD {C0D8525C-C886-442A-BAE7-3D4CDF96EB88}
DLD {7BC9C2F8-D281-4797-8FB7-FA057BBD1C6B}
DLD {B15F8836-33F0-4E32-8E18-08CB5987AE28}
DLD {8BDCEB76-A7DC-47D1-8A7A-58D7D3114A6B}
DLD {BB6EBFA6-3354-437F-B494-17BF7C4A7E64}
DLD {95E820F6-B0EC-4493-8CA2-8AA6D042F9D0}
DLD {A5CB8FFF-2E41-43C9-BB15-9716FA18A276}
DLD {44DDE780-4E6B-41A4-8676-8DE61E0E3590}
DLD {A19CE6F5-1963-49F7-B632-F3FCD290152F}
DLD {679EDA99-574F-4D22-A5B4-C384AD8B3637}
DLD {946B9B05-D47C-40CF-88F1-F25FC9DDA943}
DLD {0818B373-CCC2-4A48-8FF8-1A1A78EDFA0B}
DLD {28609622-627A-4956-8932-475B00121AE8}
DLD {3F9E1A79-1A70-4E32-B3EE-B19E5C865DF7}
DLD {27412317-F992-41A0-B558-4B8015DE18A8}
DLD {DD1259BA-3F0B-4AD4-A36C-1F25A679A096}
DLD {10B4E648-5255-4B7F-A08C-D9A5C520F7E0}
DLD {539AC2FE-906E-42AC-9692-57E37B15A417}
DLD {F6487B99-B52C-4661-85F1-D1D55DD08D47}
DLD {A02BB755-1ED5-4960-AE35-196B7ED98141}
DLD {A82C23A8-206D-44B7-9E96-E60104A805D6}
DLD {7DB35EA9-5D7E-453B-B17F-DE042B23425B}
DLD {264E5836-40FB-482B-9861-B9E586C1F3C8}
DLD {7F805546-14D7-4B3E-B0EC-98B7E80EA845}
DLD {612C1D4A-8A77-4579-B55E-AC8C4217EBF2}
DLD {4664A97E-7EDA-4644-9077-7647D3AA959D}
DLD {0AEA484E-F2C5-4929-9A77-43CED5BA3750}
DLD {5A85D50E-D486-4F3A-BE4E-D88C155D25DA}
DLD {F1D9C6A0-925A-4CAE-A49E-BEB460B13903}
DLD {BC02DB71-84F9-4561-9568-5C4A780189A7}
DLD {461B4E26-2CAD-402F-B930-E88BEECDE8B0}
DLD {BD375C67-7291-48C9-A561-A21C8969D004}
DLD {43AA2087-C9A3-4E35-99BE-649712E757A7}
DLD {0B34E519-AE87-4501-B6AD-C893E61144B6}
DLD {24FE672D-84FB-40D8-BED7-17897713FC06}
DLD {344003B4-238F-4164-8BB2-CF3EBE95B954}
DLD {4D02C7BD-21DD-4ED7-80FB-4AD2C84F2898}
DLD {130D7BEB-722B-437A-85A5-6C370B0A4964}
DLD {1621C8EC-BAF9-4DDB-AD00-9BE812433F4D}
DLD {F8E07215-D613-4AC3-B714-45512363BEFC}
DLD {3C522283-D940-4CC8-8FC3-1E3C38185834}
DLD {7176BA9D-B4E7-4C59-85EA-475C16F3957F}
DLD {7E74954E-6D28-4D7A-A914-7D2A667501DC}
DLD {CAF26AC8-DDC6-463D-90F2-7CCEDD1CCD68}
DLD {7F7750D4-CDDC-475B-97A0-400A81A009CD}
DLD {4674DE06-91AE-46E6-868E-00568529442D}
DLD {34BC0737-84A3-44F5-8A5D-8EC25AB029AB}
DLD {62C8B4F2-8812-4C65-B4BC-BCE43924C69C}
DLD {5B709B50-0B8B-4744-84F6-3E294CAAE974}
DLD {BCC0F0C4-C0DF-4318-BB69-40AF349E4ABC}
DLD {5E1630FA-8C35-43CE-A458-770498E7590B}
DLD {BD77DB4C-899F-416F-86B7-542D6FE4D5CB}
DLD {9465EB9F-9040-4E0F-B3E7-001EB4739E56}
DLD {AFCD0213-73B5-417A-AE88-419CFD016562}
DLD {9E8FC609-C7C2-4663-B729-D3F19A7BDF96}
DLD {8CE87C9A-DDEC-4F79-A38E-7883DC5BF643}
DLD {87F5292A-4890-47AB-9AEA-45A96E2E9604}
DLD {1F5C5CDF-C62E-491D-B75F-F657B4569F6E}
DLD {91EEBA3C-A09D-43C5-BB62-08E1B343785E}
DLD {E48CBE4F-3018-4A8F-804C-2FE253B35D83}
DLD {EF6570E7-6797-4E6C-95B9-C45AE857CA8C}
DLD {092030A2-EB83-4842-B6FE-30098B042100}
DLD {7CD6756E-7A98-446D-9650-A0E49DC8E6B1}
DLD {E3E2C4FC-3347-473C-A0FF-C0F1E7A7966B}
DLD {23F27548-524D-42C8-90AD-B602C2124EFE}
DLD {3392D316-F080-4A12-A2C0-1D50F12DA8A4}
DLD {9AAEBCC8-A330-47FA-A506-889F8FE3A684}
DLD {7D18521A-393D-4F8C-A85C-26B89D00BCD4}
DLD {0592F5AA-AD69-442B-A1F8-5B8A2EEA226E}
DLD {003AFF28-9C71-41EA-B5CF-E2D5D9FC2788}
DLD {9405EA35-A9A8-4819-8E89-399EF71DC817}
DLD {169DE6CD-59A3-45E3-8475-75A80C3D4161}
DLD {911CE6C4-03D4-48F2-A3B0-FCE8A2651CA2}
DLD {9EA20FEA-14D1-45E1-8485-362C0A3F6D1B}
DLD {48B17E5B-C386-4537-A8E9-7CC4F37D4565}
DLD {8C60C658-FFF7-48AF-8662-0E1ABB22CE4B}
DLD {B950D88F-8DC5-42EF-B4E0-B38DADDD092D}
DLD {087A2E64-36E3-4328-91D4-8F2C98DF4821}
DLD {FF954B89-BF06-4459-95FE-E3E85863388E}
DLD {188658A4-DE0E-4756-964C-5277EB054997}
DLD {65489C63-679F-4D6D-8A49-664A328C7F5B}
DLD {D1FE1A15-049B-481C-8608-9AD33A51FD3B}
DLD {2913EEB5-BD79-42B1-99F2-17B710C11CCD}
DLD {FE1749B6-89E6-4F22-95D2-7C55AABA6F7D}
DLD {2EBE61D3-637D-455D-A145-CFF3CA550BAE}
DLD {01E3325F-E84F-4042-BB14-B42194C05C04}
DLD {E26CAE20-34CD-437C-90A2-218E4354FA4A}
DLD {24FBE4B6-1A6D-40E4-B806-0425E26D0DB2}
DLD {40EE81BD-FC21-49A3-AFEC-8F3B17E93B5E}
DLD {54E094B2-A103-4634-854D-DA7E848569B5}
DLD {********-B5F8-4CFC-80E1-EEE1B9DD8777}
DLD {2C8ECF96-EAD0-4C00-A55E-FE6C3A9B3DE9}
DLD {7C959D69-1CB2-4088-926C-FEA1FFA37251}
DLD {B4B590AB-54CE-4623-AB71-901E897B591E}
DLD {6A982823-23F7-4EFA-A538-BC97B4418446}
DLD {********-6EDA-4E08-9C2A-3E5A9877E684}
DLD {46B2114A-6D6C-4A3F-AD8D-FC1B7B3486F5}
DLD {3E84DA16-E9F7-4B68-8A22-0E02E19DBE0C}
DLD {20F272CA-1D9C-4048-A1FD-7A692FF4A6FF}
DLD {F3D2F841-AB9E-45DA-A4C1-1D030113EE7C}
DLD {7848B099-CC33-49ED-AFA5-A94AD5046336}
DLD {10FE5279-5FFA-4E3D-B295-938E5A274A6C}
DLD {AF1C4BC1-BA6C-4F22-8201-265AC4206321}
DLD {FC59ED36-2452-4E1D-96C8-2463A84D557C}
DLD {8681A0FA-6D84-4523-966A-7E6574BCDD59}
DLD {E6834A05-3F1F-4AC5-968D-417084A1AD2A}
DLD {EF822843-473E-4E0D-A3C7-FF65F950F7AB}
DLD {DCA754F8-582D-4E6F-8500-4549085AFA98}
DLD {146AE814-8A5E-4EFD-AE5E-33BB197CF4F8}
DLD {401FD367-A8A5-4789-B866-1A23956583DC}
DLD {C23AD215-E054-4BD2-B1F5-55C8AED41535}
DLD {0BFA460A-424D-4572-9E55-DBE0467E76DB}
DLD {91B73FEC-7FDF-4547-B9D7-51C6B0F5DE3B}
DLD {16B2E9A8-7E85-460D-A6FA-A4C37B30B718}
DLD {3A9C332A-5237-4A8D-B39C-04F0C3333FDF}
DLD {CE5AF2F1-72D8-4E39-BB44-3D1EAF09EA7F}
DLD {A5BA0DEF-5159-45CE-93A9-3B837592381E}</a:History>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes
DeploymentMode=REP

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=ai_agent_test_20250619.sql
GenScriptName0=openapi_event_inte_20250301.sql
GenScriptName1=sys_data_model_20250301.sql
GenScriptName2=page_20250301.sql
GenScriptName3=page_20250212.sql
GenScriptName4=openapi_event_inte_20250210.sql
GenScriptName5=sys_data_model_20250211.sql
GenScriptName6=logic_20250212.sql
GenScriptName7=sql.sql
GenScriptName8=
GenScriptName9=
GenPathName=D:\wks\tech_middle_platform\aiagent\design\
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=UTF8
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=No
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=No
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%TABLE%_AK
IndxPreserve=Yes
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
EstimationYears=0
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes

[FolderOptions\CheckModel]

[FolderOptions\CheckModel\Package]

[FolderOptions\CheckModel\Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CheckPackageMissTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckPackageMissTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularReference]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ConstraintName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CnstMaxLen]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularDependency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ShortcutUniqCode]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table]

[FolderOptions\CheckModel\Table\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqIndex]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - INDXCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - KEYCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyCollYesYes]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\TableIndexes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table\CheckTablePartitionKey]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableStartDate]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableRefNoLifecycle]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableSourceMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTablePartialColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableKeyColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableNotOnLifecycleTablespace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MYSQL50_Table_Table_storage_type]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column]

[FolderOptions\CheckModel\Table.Column\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DomainDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnMandatory]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyDttpDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyCheckDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncNoKey]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncDttp]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\SerialColumnFK]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnCompExpr]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnOneToOneMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnDataTypeMapping]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnNoMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Auto_increment_key]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Datatype_attributes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index]

[FolderOptions\CheckModel\Table.Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UndefIndexType]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IQIndxHNGUniq]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MYSQL50_Index_Fulltext_indexes_validity]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key]

[FolderOptions\CheckModel\Table.Key\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MultiKeySqnc]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger]

[FolderOptions\CheckModel\Table.Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index]

[FolderOptions\CheckModel\Join Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View]

[FolderOptions\CheckModel\View\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\View.View Index]

[FolderOptions\CheckModel\View.View Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference]

[FolderOptions\CheckModel\Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\Reflexive]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\EmptyColl - RFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\IncompleteJoin]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\JoinOrder]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference]

[FolderOptions\CheckModel\View Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\EmptyColl - VRFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain]

[FolderOptions\CheckModel\Domain\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default]

[FolderOptions\CheckModel\Default\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltValeEmpty]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltSameVale]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User]

[FolderOptions\CheckModel\User\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Group]

[FolderOptions\CheckModel\Group\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Role]

[FolderOptions\CheckModel\Role\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure]

[FolderOptions\CheckModel\Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\ProcBodyEmpty]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\DBMS Trigger]

[FolderOptions\CheckModel\DBMS Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DbmsTriggerEvent]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source]

[FolderOptions\CheckModel\Data Source\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\EmptyColl - MODLSRC]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DtscTargets]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckDataSourceModels]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning]

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning]

[FolderOptions\CheckModel\Vertical Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing]

[FolderOptions\CheckModel\Table Collapsing\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\EmptyColl - TargetTable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact]

[FolderOptions\CheckModel\Fact\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - MEASCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - ALLOLINKCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CubeDupAssociation]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension]

[FolderOptions\CheckModel\Dimension\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - HIERCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDupHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDefHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association]

[FolderOptions\CheckModel\Association\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\EmptyColl - Hierarchy]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute]

[FolderOptions\CheckModel\Dimension.Attribute\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure]

[FolderOptions\CheckModel\Fact.Measure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute]

[FolderOptions\CheckModel\Fact.Fact Attribute\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy]

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym]

[FolderOptions\CheckModel\Synonym\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\EmptyColl - BASEOBJ]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type]

[FolderOptions\CheckModel\Abstract Data Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtInstantiable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtAbstractUsed]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure]

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\AdtProcUniqName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package]

[FolderOptions\CheckModel\Database Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - PROCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - CURCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - VARCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - TYPCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - EXCCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure]

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence]

[FolderOptions\CheckModel\Sequence\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor]

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Variable]

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type]

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception]

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace]

[FolderOptions\CheckModel\Tablespace\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage]

[FolderOptions\CheckModel\Storage\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database]

[FolderOptions\CheckModel\Database\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service]

[FolderOptions\CheckModel\Web Service\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation]

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle]

[FolderOptions\CheckModel\Lifecycle\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecyclePhase]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecycleRetention]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckPartitionRange]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase]

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIQTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDuplicateTbspace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspaceCurrency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseRetention]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIdlePeriod]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDataSource]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseExternalOnFirst]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Replication]

[FolderOptions\CheckModel\Replication\PartialReplication]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule]

[FolderOptions\CheckModel\Business Rule\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\EmptyColl - OBJCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object]

[FolderOptions\CheckModel\Extended Object\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link]

[FolderOptions\CheckModel\Extended Link\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File]

[FolderOptions\CheckModel\File\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckPathExists]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format]

[FolderOptions\CheckModel\Data Format\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckDataFormatNullExpression]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Architecture Area]

[FolderOptions\CheckModel\Architecture Area\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Architecture Area\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Architecture Area\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Architecture Area\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Architecture Area\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Architecture Area\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Architecture Area\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
UseTerm=No
EnableRequirements=No
EnableFullShortcut=Yes
SynchroCode=Yes
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\AREA]

[ModelOptions\Physical Objects\ClssNamingOptions\AREA\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\AREA\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Xsm]
GenRootElement=Yes
GenComplexType=No
GenAttribute=Yes
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryFilename>D:\wks\tech_middle_platform\aiagent\design\aiAgentTest.pdm</a:RepositoryFilename>
<c:GenerationOrigins>
<o:Shortcut Id="o3">
<a:ObjectID>E75BF93D-4E12-4435-B32C-C77AEC975009</a:ObjectID>
<a:Name>aiAgent</a:Name>
<a:Code>aiAgent</a:Code>
<a:CreationDate>1750296244</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296244</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>********-2EB4-4CDC-B4E1-6489961808EC</a:TargetID>
<a:TargetClassID>5F45F978-C4F3-4E35-A3FC-AF3318663A0F</a:TargetClassID>
</o:Shortcut>
</c:GenerationOrigins>
<c:DBMS>
<o:Shortcut Id="o4">
<a:ObjectID>177F4623-AD2B-4ECE-8AB1-6FDC6D458830</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1746529264</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746529264</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o5">
<a:ObjectID>F898B697-B1C8-4DE6-8B33-6E1548D89F09</a:ObjectID>
<a:Name>aiAgentTest</a:Name>
<a:Code>aiAgentTest</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {226A2AB4-F37D-45D0-BF3D-C8F84F7320FF}
DAT **********
ORG {41EED29D-7665-415D-B0E0-FC0504B8B5E3}
DAT **********
ATT DIAGNAME
ATT DISPNAME
ATT CODE
ATT DPRF</a:History>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Show Links intersections=Yes
Activate automatic link routing=Yes
Grid size=800
Graphic unit=2
Window color=255 255 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255 255 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Show Icon=No
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;#$%&amp;&#39;)*+,-./:;=&gt;?@\]^_`|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
Area.IconPicture=Yes
Area.Stereotype=Yes
Area.Comment=No
Area.TextStyle=No
Area.SubSymbols=Yes
Area_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Architecture Area Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject.TextStyle=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ExtDpdShowStrn=Yes
ExtendedDependency_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.TextStyle=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Package.Stereotype=Yes
Package.Comment=No
Package.IconPicture=No
Package.TextStyle=No
Package_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=Yes
Table.IconPicture=No
Table.TextStyle=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No\r\nCode Yes&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;\&amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;\&amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View.TextStyle=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure.TextStyle=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=Yes
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
Entity.Stereotype=Yes
Entity.Attributes=Yes
Entity.Attributes._Filter=&quot;All attributes&quot; CDMPENTALL
Entity.Attributes._Columns=Stereotype IdentifierIndicator DomainOrDataType NullIndicator
Entity.Attributes._Limit=-5
Entity.Identifiers=Yes
Entity.Identifiers._Columns=Stereotype IdentifierIndicator
Entity.Comment=No
Entity.IconPicture=No
Entity.TextStyle=No
Entity_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Attributes&quot; Collection=&quot;Attributes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIdentifierIndicator No &amp;quot;Identifier indicators&amp;quot;\r\nDataType No\r\nDomainOrDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nNullIndicator No Mandatory&quot; Filters=&quot;&amp;quot;All attributes&amp;quot;  CDMPENTALL &amp;quot;&amp;quot;\r\n&amp;quot;Primary attributes&amp;quot;  CDMPENTPK &amp;quot;\&amp;quot;PIDTF \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;\r\n&amp;quot;Identifying attributes&amp;quot;  CDMPENTIDTF &amp;quot;\&amp;quot;AIDF \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Identifiers&quot; Collection=&quot;Identifiers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIdentifierIndicator No &amp;quot;Identifier indicators&amp;quot;&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Relationship.Entity1ToEntity2Role=Yes
Relationship.Entity2ToEntity1RoleCardinality=No
Relationship.Entity1ToEntity2RoleDominant=Yes
Relationship.Stereotype=Yes
Relationship.DisplayName=Yes
Relationship.JoinExpression=No
Relationship.Entity2ToEntity1Role=Yes
Relationship.Entity1ToEntity2RoleCardinality=No
Relationship.Entity2ToEntity1RoleDominant=Yes
Relationship_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Role&quot; Attribute=&quot;Entity1ToEntity2Role&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Role&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Entity2ToEntity1RoleCardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cardinality&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Dominance&quot; Attribute=&quot;Entity1ToEntity2RoleDominant&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Dominance&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Role&quot; Attribute=&quot;Entity2ToEntity1Role&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Role&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Entity1ToEntity2RoleCardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cardinality&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Dominance&quot; Attribute=&quot;Entity2ToEntity1RoleDominant&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Dominance&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
Inheritance.Stereotype=Yes
Inheritance.DisplayName=Yes
Inheritance.IconPicture=No
Inheritance.TextStyle=No
Inheritance_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Association.Stereotype=Yes
Association.Comment=No
Association.Attributes=Yes
Association.Attributes._Columns=Stereotype DataType NullIndicator
Association.Attributes._Limit=-5
Association.IconPicture=No
Association.TextStyle=No
Association_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Attributes&quot; Collection=&quot;Attributes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nDomainOrDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nNullIndicator No Mandatory&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
AssociationLink.SymbolCardinality=Yes
AssociationLink.Stereotype=Yes
AssociationLink.Role=Yes
AssociationLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;SymbolCardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cardinality&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Role&quot; Attribute=&quot;Role&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\AREA]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=9600
Height=8000
Brush color=253 249 234
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=16
Brush gradient color=245 230 173
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 121 98 6
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0
Source Arrow=24
Center Arrow=24
Target Arrow=0

[DisplayPreferences\Symbol\USRDEPD]
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0
Source Arrow=24
Center Arrow=24
Target Arrow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LCNMFont=新宋体,8,N
LCNMFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0 0 0
KeysFont=新宋体,8,N
KeysFont color=0 0 0
IndexesFont=新宋体,8,N
IndexesFont color=0 0 0
TriggersFont=新宋体,8,N
TriggersFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0 0 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=新宋体,8,N
IndexesFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0
Source Arrow=24
Center Arrow=24
Target Arrow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0
Source Arrow=24
Center Arrow=24
Target Arrow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0
Source Arrow=24
Center Arrow=24
Target Arrow=0

[DisplayPreferences\Symbol\LDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\LDMENTT]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
AttributesFont=新宋体,8,N
AttributesFont color=0 0 0
EntityPrimaryAttributeFont=新宋体,8,U
EntityPrimaryAttributeFont color=0 0 0
EntityForeignAttributeFont=新宋体,8,N
EntityForeignAttributeFont color=0 0 0
IdentifiersFont=新宋体,8,N
IdentifiersFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=176 186 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 88 74 181
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\LDMRLSH]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 88 74 181
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\LDMINHR]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=Yes
Width=1600
Height=1000
Brush color=176 186 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\LDMLINH]
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\CDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\ENTT]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
AttributesFont=新宋体,8,N
AttributesFont color=0 0 0
EntityPrimaryAttributeFont=新宋体,8,U
EntityPrimaryAttributeFont color=0 0 0
IdentifiersFont=新宋体,8,N
IdentifiersFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=176 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 170 170
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\RLSH]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 170 170
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\ASSC]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AttributesFont=新宋体,8,N
AttributesFont color=0 0 0
EntityPrimaryAttributeFont=新宋体,8,U
EntityPrimaryAttributeFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\LINK]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\CDMINHR]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=Yes
Width=1600
Height=1000
Brush color=176 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\LINH]
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\LDM]

[DisplayPreferences\CDM]</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:ReferenceSymbol Id="o6">
<a:ModificationDate>1750298149</a:ModificationDate>
<a:Rect>((-15060,-6746), (-9860,-5150))</a:Rect>
<a:ListOfPoints>((-10882,-6121),(-14038,-6121))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o7"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o8"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o9"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o10">
<a:ModificationDate>1750298081</a:ModificationDate>
<a:Rect>((-70690,6152), (-30248,14634))</a:Rect>
<a:ListOfPoints>((-70290,13673),(-33448,13673),(-33448,6552))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o11"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o8"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o12"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o13">
<a:ModificationDate>1750298081</a:ModificationDate>
<a:Rect>((-80905,20307), (-74505,27182))</a:Rect>
<a:ListOfPoints>((-77705,26782),(-77705,20707))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o11"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o15"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o16">
<a:ModificationDate>1750298082</a:ModificationDate>
<a:Rect>((-70690,16869), (-62349,18459))</a:Rect>
<a:ListOfPoints>((-62749,17494),(-70290,17494))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o17"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o11"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o18"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o19">
<a:ModificationDate>1750298082</a:ModificationDate>
<a:Rect>((-68273,30742), (-61873,32332))</a:Rect>
<a:ListOfPoints>((-67398,31367),(-62749,31367))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o17"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o20"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o21">
<a:ModificationDate>1750298082</a:ModificationDate>
<a:Rect>((-52354,6152), (-45954,17294))</a:Rect>
<a:ListOfPoints>((-49154,16894),(-49154,6552))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o17"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o8"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o22"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o23">
<a:ModificationDate>1750298146</a:ModificationDate>
<a:Rect>((-80905,-411), (-74505,6561))</a:Rect>
<a:ListOfPoints>((-77705,-11),(-77705,6161))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o24"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o11"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o25"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o26">
<a:ModificationDate>1750298087</a:ModificationDate>
<a:Rect>((38340,25858), (44740,30089))</a:Rect>
<a:ListOfPoints>((41540,29689),(41540,26258))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o27"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o28"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o29"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o30">
<a:ModificationDate>1750298087</a:ModificationDate>
<a:Rect>((12090,16679), (29787,29831))</a:Rect>
<a:ListOfPoints>((15290,29431),(15290,17304),(29387,17304))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o31"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o28"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o32"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o33">
<a:ModificationDate>1750297992</a:ModificationDate>
<a:Rect>((29250,-9632), (35650,-8036))</a:Rect>
<a:ListOfPoints>((31506,-9007),(33394,-9007))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o34"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o35"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o36"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o37">
<a:ModificationDate>1750298084</a:ModificationDate>
<a:Rect>((-19645,32132), (-13245,49093))</a:Rect>
<a:ListOfPoints>((-16326,32532),(-16326,48650),(-16565,48650),(-16565,32532))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o38"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o38"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o39"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o40">
<a:ModificationDate>1750298084</a:ModificationDate>
<a:Rect>((-37930,30742), (-31530,32332))</a:Rect>
<a:ListOfPoints>((-33902,31367),(-35559,31367))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>11184640</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o38"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o17"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o41"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o42">
<a:ModificationDate>1750298147</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-69078,-17420), (-54660,-174))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o43"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o8">
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-53176,-18794), (-14038,6552))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o44"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o7">
<a:ModificationDate>1750298149</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-10882,-19631), (8892,8415))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o45"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o24">
<a:ModificationDate>1750298146</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-89253,-17257), (-73187,-11))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o46"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o14">
<a:ModificationDate>1750298080</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-87996,26782), (-67398,44928))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o47"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o11">
<a:ModificationDate>1750298081</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-85120,6161), (-70290,20707))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o48"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o17">
<a:ModificationDate>1750298082</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-62749,16894), (-35559,45840))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o49"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o38">
<a:ModificationDate>1750298084</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-33902,17904), (292,46850))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o50"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o28">
<a:ModificationDate>1750298087</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((29387,9162), (53693,26258))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o51"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o27">
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((31174,29689), (48064,43185))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o52"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o53">
<a:ModificationDate>1750298087</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((10661,814), (26315,14310))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o54"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o31">
<a:ModificationDate>1750298086</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((2931,29431), (27649,45627))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o55"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o35">
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((33394,-17105), (49460,-909))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o56"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o34">
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((10908,-21147), (31506,-4051))</a:Rect>
<a:LineColor>11184640</a:LineColor>
<a:FillColor>16777136</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o57"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o5"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o43">
<a:ObjectID>F5BB9B4E-9578-4BBB-A26C-45A5A3FE4893</a:ObjectID>
<a:Name>工具</a:Name>
<a:Code>ai_tools</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>工具</a:Comment>
<a:History>ORG {36680C23-EF83-4A6B-B421-D13BAD40CEF1}
DAT **********
ORG {C8FD639D-E054-48E5-A86F-179AC8DAADDF}
DAT **********
ATT PIDTF</a:History>
<a:Stereotype>ai_tools</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o58">
<a:ObjectID>DA620FD4-3059-45E7-ABF7-84218BE1984F</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {CB778D2C-A050-4618-95C1-0E8AF5D8F611}
DAT **********
ORG {B54C9247-A9C9-4A93-AF00-90767E1E25C7}
DAT 1746528931
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>AEC770E8-9D0B-4E80-B781-A69226285C5E</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>名称</a:Comment>
<a:History>ORG {3026B6AE-4E24-4D37-B79C-886797629ACC}
DAT **********
ORG {F185A9F9-DA13-4A78-855E-FB71EAF9C4FE}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>C46045A4-4426-47EF-910A-45B5E1912972</a:ObjectID>
<a:Name>类型</a:Name>
<a:Code>tool_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>类型</a:Comment>
<a:History>ORG {9F09A5A9-FA7B-49A1-8F39-FD9D156F3810}
DAT **********
ORG {2208E14A-CB5A-4A7D-A5D1-E2E28E9837A8}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>3B1ED53A-FAE9-4925-87C9-8DD610CD530F</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>description</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>描述</a:Comment>
<a:History>ORG {8308AA2C-EC4C-45B3-B4CA-6CDA13AA31DD}
DAT **********
ORG {B2C4D7B3-7D9A-44EE-8E30-1583820CE47F}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>8A30DC0D-07EC-4E90-9511-2C15CD23D90D</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {B5E2EB66-CC38-42B1-A338-C67E0C4AD50C}
DAT **********
ORG {6B2C7E22-C4F5-47AF-87C4-CA0D3896C560}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>985D2ECD-EFA0-4270-8602-06309E0DBDA5</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {E2F245ED-FE8E-4A56-B547-40196AAB9922}
DAT **********
ORG {C4E27DF8-1085-4755-86EF-FF9F6FDF7BD3}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>EA2C57BB-F24D-4D8B-B784-BD37783C627A</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {91F55EE4-BC31-4B38-BED1-A64A50C9381C}
DAT **********
ORG {3B045D8D-8AE5-46B7-8AEF-4884B99FBFE0}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>578F65D2-0DCC-47BB-A299-D5914E7BBE51</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {A21E30B2-5C51-4096-8421-5EF85AF7B33A}
DAT **********
ORG {E08D4C4B-72F8-4D29-AFF6-965E8D88FAD3}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>95A76CA1-58D1-48D6-907B-4658BF1519B5</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {7E5CAB0C-3675-4D24-BE59-930AB2828CDB}
DAT **********
ORG {DDB07FDC-89EC-4FCD-A7D9-0C115EBDB1DD}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>107AC35E-142B-4EA8-A520-C1CE5D39D145</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {117239AA-E62D-48D4-8413-111B4C6A2771}
DAT **********
ORG {2B0D9C4E-E590-482E-939B-E7DEB0AC7FCC}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>D371E173-70D5-46B8-B115-973836CB5026</a:ObjectID>
<a:Name>备注</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>备注</a:Comment>
<a:History>ORG {F305AF75-8D96-4495-99BB-3C2AD360B234}
DAT **********
ORG {4862E733-CFAF-4C64-9998-B1D088055532}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>971E1E95-09EB-4AE3-B4A9-95F8D38074E8</a:ObjectID>
<a:Name>排序</a:Name>
<a:Code>sort</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>排序</a:Comment>
<a:History>ORG {E4BC2503-0EC5-41EF-90B2-C1327D06CF63}
DAT **********
ORG {30B2E042-9E95-429E-B27A-2C091347B947}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>9CEB26BF-E239-4C3D-B517-A8DA01651333</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {960C3870-59B8-43EE-BA0F-3817E13454EE}
DAT **********
ORG {B2EA8CAF-442C-4E92-B56E-607EE959EE26}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>F7AB9F29-4009-4B41-A0A3-7732556EF94D</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {1C527419-354C-43A2-B0B0-F6DE7C78D527}
DAT **********
ORG {EEC07F2A-B4B0-4A0D-9F2B-57C719C4ADC6}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>1428BDA9-7F40-42FB-829C-666D3ED056A4</a:ObjectID>
<a:Name>工具指令</a:Name>
<a:Code>command</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>工具指令</a:Comment>
<a:History>ORG {00CB7B4F-C91E-412C-A7E9-0E3CBEE08415}
DAT 1750296238
ORG {EBF6EDB9-80AA-4DCE-8095-87D2EC50A22B}
DAT 1750296213
ATT LABL</a:History>
<a:DataType>smallint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o73">
<a:ObjectID>E49AA2F2-C24D-46F6-B7A8-3DDD86390934</a:ObjectID>
<a:Name>pk_ai_tools</a:Name>
<a:Code>pk_ai_tools</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {6D665585-61D8-4BEA-80B9-EA5E84294EE3}
DAT **********
ORG {A7FBE206-6F01-40A8-B86D-11F75AB554EC}
DAT 1746528931</a:History>
<c:Key.Columns>
<o:Column Ref="o58"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o74">
<a:ObjectID>99E71972-3357-4E7C-B813-8D7432E4859B</a:ObjectID>
<a:Name>ai_tools_PK</a:Name>
<a:Code>ai_tools_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o73"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o75">
<a:ObjectID>B37204D7-BA5E-4A63-9956-4AA0DAAC6267</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o58"/>
</c:Column>
<c:Elements>
<o:Column Ref="o58"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o73"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o44">
<a:ObjectID>CE8CEAE1-246C-43A9-A1A7-5A865337B1B8</a:ObjectID>
<a:Name>应用</a:Name>
<a:Code>ai_application</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750298160</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用</a:Comment>
<a:History>ORG {8FD7F6E2-3EBE-40F7-97A5-D9DDD44240E9}
DAT **********
ORG {2FF90A88-0F21-4EB3-95E4-A4FD6F155DD5}
DAT **********</a:History>
<a:Stereotype>ai_application</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o76">
<a:ObjectID>7F5C06B8-5788-410C-9DFD-E9ED63F9E351</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {F61FC702-BF78-4CE4-9D9E-0478DC759AAA}
DAT **********
ORG {0B60C648-AB7B-45B9-A685-9EB6C584ECE1}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>7AD49550-D3BD-4608-B6A9-33DEDB92865D</a:ObjectID>
<a:Name>环境ID</a:Name>
<a:Code>env_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>环境ID</a:Comment>
<a:History>ORG {75A1D8D9-4E64-4979-9491-3B978402074E}
DAT **********
ORG {529DC47C-CF9E-4DF6-A5EC-1A19B4AC18FA},{7846B76D-26C1-47F5-A155-35ACF0E81BA8}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>1100DA3A-BB96-4675-B34F-411827449900</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>code</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>编码</a:Comment>
<a:History>ORG {167C8B3E-EE35-45CF-BE8E-9A8EA598E720}
DAT **********
ORG {3CE39921-9B6B-4AAF-A9D0-B1FFE9DEB8F2}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>0AE52D2A-1F37-4059-B785-4DB1FD9D4973</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>名称</a:Comment>
<a:History>ORG {0BEFD49C-B902-43C5-A87C-534A8B030EAD}
DAT **********
ORG {3F49AAEE-5FD6-44E5-9B23-2E843BC33511}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>91E72817-508B-4889-8324-CC01CADC695F</a:ObjectID>
<a:Name>平台-PC1|H5 2|Android 3|IOS 4|鸿蒙5</a:Name>
<a:Code>platform</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>平台-PC1|H5 2|Android 3|IOS 4|鸿蒙5</a:Comment>
<a:History>ORG {105D35B8-29AC-4F77-885A-9F05771B3D3D}
DAT **********
ORG {D3F99668-F31E-4065-A718-0D81F5350757}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39;1&#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>5EDAD4FB-8425-4B23-A58B-889044AB80D4</a:ObjectID>
<a:Name>应用分类</a:Name>
<a:Code>app_category</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用分类</a:Comment>
<a:History>ORG {B1E83DB8-611A-4DD2-9F48-B20569B9556C}
DAT **********
ORG {5B5F6A50-CFAE-46EF-9808-386DB30664AF}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>23ACB760-3F9D-4BC0-88AD-C2335C18E976</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>description</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>描述</a:Comment>
<a:History>ORG {E22BBF97-093E-45D3-AC38-5B4B86CEE532}
DAT **********
ORG {CA78F5A2-A59A-4575-BE7E-C623AA9C8623}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>7B86F9C3-8999-4096-9FF6-50A7BD9B198D</a:ObjectID>
<a:Name>应用图标</a:Name>
<a:Code>app_icon</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用图标</a:Comment>
<a:History>ORG {71EDB763-E124-489C-AD56-387B90617137}
DAT **********
ORG {C423D74B-71F0-4D57-B9FC-3510447A9402}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>0A3852CE-452F-4C0E-9700-ABCF42FFA936</a:ObjectID>
<a:Name>1信息技术|2制造业|3零售业|4服务业|5信息技术|6医疗行业|7教育行业|8建筑业|9农业</a:Name>
<a:Code>industry</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>1信息技术|2制造业|3零售业|4服务业|5信息技术|6医疗行业|7教育行业|8建筑业|9农业</a:Comment>
<a:History>ORG {35E932B4-D85C-4BED-ACDD-A01D99C5938F}
DAT **********
ORG {CFA6F18B-C50A-4B80-84EC-B884DB984703}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>4A069798-2FE4-462E-B46B-6AB6CDF9402D</a:ObjectID>
<a:Name>业务功能-1运营管理|2财务管理|3市场营销|4人力资源|5客户服务</a:Name>
<a:Code>business</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>业务功能-1运营管理|2财务管理|3市场营销|4人力资源|5客户服务</a:Comment>
<a:History>ORG {DA9A417B-2133-4B67-A69A-5A599BA0A68A}
DAT **********
ORG {1A0A6829-36CD-45D1-BBB4-37B0855B46D5}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>B462BF07-927A-4998-B15F-8FA54A7FEAFE</a:ObjectID>
<a:Name>应用类型-1Web应用|2移动应用|3桌面应用|4集成应用</a:Name>
<a:Code>app_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用类型-1Web应用|2移动应用|3桌面应用|4集成应用</a:Comment>
<a:History>ORG {51890290-A0B4-4EA0-A479-B695405ABB19}
DAT **********
ORG {86623325-62FF-49C6-B793-C6FE6ABFC4CD}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>363A8390-45BB-4601-8111-442042C8F27A</a:ObjectID>
<a:Name>发布版本</a:Name>
<a:Code>release_version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>发布版本</a:Comment>
<a:History>ORG {8F4B0F05-2BAC-472F-834D-767E7D68A63D}
DAT **********
ORG {E2A7C05E-BB84-4645-B332-22DDEE05B6E8}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>9CD9D24B-E16B-497D-B136-9871186DBE3C</a:ObjectID>
<a:Name>应用版本</a:Name>
<a:Code>app_version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用版本</a:Comment>
<a:History>ORG {56CA1B78-4DFC-4FE3-9574-F8227C8ADA88}
DAT **********
ORG {9AF10C66-D9C0-4F43-AF65-769AE0BDF44E}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>E37D8FF2-106C-4286-891C-E062A0D14483</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {7C90B1E5-DA7E-4F1E-9434-13AA27D4D0E4}
DAT **********
ORG {942F574E-3456-41DC-B0F3-6B5FED33CDC9}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>62E5E6E7-98D2-496A-94FA-23221ED63846</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {CA4DCF0E-211D-4784-8333-CADD7C4A6DAF}
DAT **********
ORG {B0FDE189-889F-41DC-99AE-E141435514FC}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>29C6C266-40C4-4F95-BFF0-CCA83A88D9E7</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {3E2D20A1-A0BA-46B5-B535-76094C18E114}
DAT **********
ORG {8F6D95C8-AD4A-4B9E-A4D8-D82362F8F74D}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>3C9F0556-67C1-467B-B71E-818BD58B007A</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {BB83A6BB-068E-457A-B726-A3CF131B05AD}
DAT **********
ORG {77E15C1B-5DE8-42B2-90F1-D097E3EC9C7B}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>FE77592D-84A6-4470-A768-AFCEB68C0F96</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {A4955D71-ABBE-4385-B034-6B2BB0CEEE8F}
DAT **********
ORG {D7625BE5-C471-4756-99F9-A83965F1B621}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>2D44A14A-63D2-4452-8F42-923FBA6C44BA</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {D8B228B6-A73E-47D9-8FDB-CA7ADA91A1E7}
DAT **********
ORG {D53AA5F7-2CD6-473A-80AF-D956D006BD44}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>547EE45B-3754-4FCF-B870-F1D29F460115</a:ObjectID>
<a:Name>备注</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>备注</a:Comment>
<a:History>ORG {C2410555-1214-49BA-85A2-39A90EFFEFDE}
DAT **********
ORG {595A197F-51B1-4C0D-85E4-B2A097553891}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>83C59C8D-8C0D-40AB-9BE5-A633526C5B2B</a:ObjectID>
<a:Name>排序</a:Name>
<a:Code>sort</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>排序</a:Comment>
<a:History>ORG {B0B9F8EA-3DFC-4796-A9C7-A529597EB67C}
DAT **********
ORG {F90019D7-BEED-4A27-AD8B-BE2C671DB7B3}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>C0671264-DCEB-4440-A365-CEBAAB33DD75</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {304526B9-3258-41A6-BA9B-BD5C2B0B7184}
DAT **********
ORG {445888C9-815C-4C12-85AF-EBA417764C05}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>0683890B-042E-4274-A95C-CA3A9237A40E</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {6228047D-43F4-4FC7-95DE-FAE3AFA657C1}
DAT **********
ORG {3B17040B-05D8-447F-B77A-5E6304D98926}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o99">
<a:ObjectID>0D27443B-169B-47A9-A1CA-3228F9C5165E</a:ObjectID>
<a:Name>pk_ai_application</a:Name>
<a:Code>pk_ai_application</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {5C8FD397-96C8-4F3C-93E5-85D54A898F8F}
DAT **********
ORG {A756F5A2-5F37-4691-B9CA-7C9EAC792BD3}
DAT **********</a:History>
<c:Key.Columns>
<o:Column Ref="o76"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o100">
<a:ObjectID>D90072FA-C757-4C9A-B73E-09FDEC11C8B6</a:ObjectID>
<a:Name>ai_application_PK</a:Name>
<a:Code>ai_application_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o99"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o101">
<a:ObjectID>CCDD6F1D-A547-4B08-AE1D-F2D06480FCCF</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o76"/>
</c:Column>
<c:Elements>
<o:Column Ref="o76"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o99"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o45">
<a:ObjectID>4E4A6C25-C138-4986-A7FD-7BADCD61C469</a:ObjectID>
<a:Name>菜单</a:Name>
<a:Code>ai_menu</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>菜单</a:Comment>
<a:History>ORG {7711F167-0141-4DFF-88CD-96197EB5BF0F}
DAT **********
ORG {E7EB6B15-B733-40FF-9088-91BAD0B4997B}
DAT **********</a:History>
<a:Stereotype>ai_menu</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o102">
<a:ObjectID>05BF147B-2930-4A8B-8D8F-D47EF1D9A3B8</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {E8883188-A6FD-4D69-B604-F02C75E6F85F}
DAT **********
ORG {4EED323B-A966-436B-AF50-B589313F4C10}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>3C5A6CD6-AF08-4088-81A3-82A50F8C07B0</a:ObjectID>
<a:Name>应用ID</a:Name>
<a:Code>app_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用ID</a:Comment>
<a:History>ORG {482C1BA1-1BD0-4AC6-A1F3-0C178BC00CCF}
DAT **********
ORG {0B60C648-AB7B-45B9-A685-9EB6C584ECE1},{4F4F8C47-61A3-4786-91DD-DB5E9EBAE331}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>A4E92EAF-1D34-4AEB-A2E7-E20B5B9A63C0</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>名称</a:Comment>
<a:History>ORG {D7C056A4-BE8E-4FD6-B2E0-48A02D0BCF63}
DAT **********
ORG {DAD66C65-AB35-4140-B112-14C37C9D6F81}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>8D88B8FA-FD88-4110-80FF-4AD3DD6C623A</a:ObjectID>
<a:Name>标签</a:Name>
<a:Code>labels</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>标签</a:Comment>
<a:History>ORG {EF70E8EB-DA62-46A4-BE9F-D2D5BA522129}
DAT **********
ORG {359E6A46-BD73-4D93-A02E-7FF392B23604}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>D01ACCE0-9C4F-4714-9F58-44F5F6A8EA52</a:ObjectID>
<a:Name>图标</a:Name>
<a:Code>icon</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>图标</a:Comment>
<a:History>ORG {283380B8-48FB-408A-800F-181715E15507}
DAT **********
ORG {0D623185-F653-421D-9420-AE9F9E591D79}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>046C800F-2787-4748-9189-BC3427DAB6A9</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>code</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>编码</a:Comment>
<a:History>ORG {68D39011-36F1-4346-B037-7E6AB194F735}
DAT **********
ORG {06E40284-5AB3-4AA7-B37F-718D51CF282F}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>99C1E3B0-D4B6-4CA7-9D7B-63286DFAB03F</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>description</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>描述</a:Comment>
<a:History>ORG {F62E64AD-CADB-45A0-A60C-19F5F3D74ECF}
DAT **********
ORG {924C46AA-8CE7-4954-8599-5D603BB01256}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>4E7F4FF6-FB59-44DD-B223-F6EC91456E62</a:ObjectID>
<a:Name>菜单资源</a:Name>
<a:Code>source</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>菜单资源</a:Comment>
<a:History>ORG {AAA89331-B871-4303-ACEE-1E10E0D8D78D}
DAT **********
ORG {8B27B6F3-A10A-4FA8-AB82-A3B1B7226007}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>2F867BAB-7501-4F82-94E9-3B86909F886F</a:ObjectID>
<a:Name>菜单类型</a:Name>
<a:Code>category</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>菜单类型</a:Comment>
<a:History>ORG {5E37627D-DE16-4236-AA3D-BADB11223432}
DAT **********
ORG {27F33C5A-3DA9-4830-90D8-4FD8F9E37833}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>19F8432F-05C0-4810-AD2C-D6F5E263ACEE</a:ObjectID>
<a:Name>按钮类型</a:Name>
<a:Code>action</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>按钮类型</a:Comment>
<a:History>ORG {5EE4E88E-64D3-49AA-B243-4480B2544940}
DAT **********
ORG {EF20AC73-33DF-473B-B114-F0F04629D40B}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>29B462DB-B578-4034-AA92-576E3079C584</a:ObjectID>
<a:Name>是否打开新页面</a:Name>
<a:Code>is_open</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否打开新页面</a:Comment>
<a:History>ORG {F3E417AC-82A2-426A-A5BF-37B08A97F385}
DAT **********
ORG {9430F6C9-B4DA-47B0-9B92-741248F2C4DD}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>20E02CBF-8C90-459A-895C-C1F57C398FE4</a:ObjectID>
<a:Name>请求地址</a:Name>
<a:Code>path</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>请求地址</a:Comment>
<a:History>ORG {A5263D1E-B4A4-4FBD-B3F2-BF0C94F0DDE1}
DAT **********
ORG {04E98DF0-C518-4D28-8C22-5636A1044198}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>418E117D-A120-4C52-9242-7E2766937DCE</a:ObjectID>
<a:Name>请求类型-0内部|1外部</a:Name>
<a:Code>request_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>请求类型-0内部|1外部</a:Comment>
<a:History>ORG {747C1288-3EE1-4C3A-B62E-4850CB475036}
DAT **********
ORG {15AF87FB-8DCB-4BC4-9F5D-3214FE170874}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>B1E84981-AEC5-44DE-B82C-02A33D363E03</a:ObjectID>
<a:Name>菜单页面id</a:Name>
<a:Code>page_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>菜单页面id</a:Comment>
<a:History>ORG {588B7964-3A0B-4E61-BBDE-F3E2926233AD}
DAT **********
ORG {D3753F09-A8D9-4A7B-92AD-9FACF85916B7}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>346E2649-88C3-4BBC-9786-9B2560C1199C</a:ObjectID>
<a:Name>父级ID</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>父级ID</a:Comment>
<a:History>ORG {1C25F028-71DB-4929-A5E2-AEB4BC79D2A3}
DAT **********
ORG {3A411DA5-6713-4390-A3F7-183F4D165A5F}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>C9EC89D4-15B2-48C4-8761-81400E3EC852</a:ObjectID>
<a:Name>层级</a:Name>
<a:Code>level</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>层级</a:Comment>
<a:History>ORG {5C7AD7D1-007F-4C5B-B675-A6FDE9C156BA}
DAT **********
ORG {1409561A-6C77-4B29-9043-09FFAFCF7065}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>2201E836-685D-4FC9-9E04-E90F0478006C</a:ObjectID>
<a:Name>排序</a:Name>
<a:Code>sort</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>排序</a:Comment>
<a:History>ORG {F2C12418-BE4F-434F-B15D-1AF2B5522AA5}
DAT **********
ORG {A62E479E-A45B-40ED-B46E-C3EE95FA5F02}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>8970726E-E21C-4180-A346-CB9BDBA5118C</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {5DB871AB-43CF-417F-BF34-AC418350C6D1}
DAT **********
ORG {9C2035EB-649F-4900-B397-B5D6EFD34E00}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>75323AA8-6F14-431E-B63A-051970800D09</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {E9A7D84F-C8F1-4864-A1A3-A6308927E0C8}
DAT **********
ORG {6CD2CBC8-7C81-4789-9F57-B185BA594F75}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>B15B0475-6C24-4D94-B72C-1EAE6FFC0C87</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {211572DA-D651-43B9-99B3-944920B1FB04}
DAT **********
ORG {5F1505C4-729C-4A4B-90CF-35D0E5837C58}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>49C8EA4A-6779-4138-B4E7-23F9C910A143</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {CCA1A1AC-DA0D-4442-B49B-1901B105F165}
DAT **********
ORG {C70FFCE7-B4EC-4031-AF28-F612C3607F7B}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>4CD146FC-DAA1-4809-931E-3073C172FC40</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {6FFF3836-2C57-4691-B2E9-98B775F8E742}
DAT **********
ORG {D574BE1E-D574-47A3-BA86-C27E1F440B51}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>D6E7926A-C712-4450-BE01-B14CC7CA72BE</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {3D24D786-B28D-4BC1-A3F9-B2B2F668987C}
DAT **********
ORG {CF1B357D-EFCD-4A72-9411-1A921D550250}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>1B29F340-8D2C-429F-A7CA-B4F05D774EB9</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {BC299019-10D0-40F2-8212-9457ABD5D1B6}
DAT **********
ORG {B8CD26E2-6239-4488-8B21-354867254E7F}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>E42F2E7B-C9A8-409E-B09D-FE7D4134EFE2</a:ObjectID>
<a:Name>备注</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>备注</a:Comment>
<a:History>ORG {1F26FA51-6E77-473C-88D8-CA5F5B11C261}
DAT **********
ORG {AD271EF7-F194-4EB6-A739-EA5A24F713A3}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>E8CDC490-FC25-413B-A2E1-6E78FFB5D322</a:ObjectID>
<a:Name>菜单版本</a:Name>
<a:Code>menu_version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>菜单版本</a:Comment>
<a:History>ORG {A3CB1184-8A67-4DF0-A76E-28F6DA2C6EC2}
DAT **********
ORG {F70CFE94-2F09-4EE5-A0A6-F25E29D3B2EE}
DAT **********
ATT LABL</a:History>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>DC0C4004-97E5-42F3-BE3E-A2D06A0080BF</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {7CC852EC-C1F2-47C3-8F02-AB13D86CBA88}
DAT **********
ORG {8DB8D091-9353-4E15-BEB2-F2DA5B3D8BFA}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o129">
<a:ObjectID>3C73502C-9ADF-4CA3-AB50-B3A815DFD5B6</a:ObjectID>
<a:Name>pk_qbp_menu_instance</a:Name>
<a:Code>pk_qbp_menu_instance</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {FC7ADEAC-7168-40A8-8A1F-CAE145057448}
DAT **********
ORG {4FDDD81F-0A57-4CAE-BAA5-DD62C059F887}
DAT **********</a:History>
<c:Key.Columns>
<o:Column Ref="o102"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o130">
<a:ObjectID>EB1631A9-36AE-4DF7-9B7E-5BFDCC7F992F</a:ObjectID>
<a:Name>ai_menu_PK</a:Name>
<a:Code>ai_menu_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o129"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o131">
<a:ObjectID>354D9E3D-BE20-4DCA-894C-BD5667C37388</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o102"/>
</c:Column>
<c:Elements>
<o:Column Ref="o102"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o132">
<a:ObjectID>ABC977C1-5CED-4734-B135-2177005CC124</a:ObjectID>
<a:Name>app_menu_rel_FK</a:Name>
<a:Code>app_menu_rel_FK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o9"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o133">
<a:ObjectID>79CEAC9C-D0DE-4763-8D70-C7D3C864AB4F</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o103"/>
</c:Column>
<c:Elements>
<o:Column Ref="o103"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o129"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o46">
<a:ObjectID>1646871F-433B-46B3-BD49-A013EB1A07ED</a:ObjectID>
<a:Name>对话元数据表</a:Name>
<a:Code>ai_conversation_meta</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>对话元数据表</a:Comment>
<a:History>ORG {298AD1B7-8516-4E68-979A-E6E065A762FA}
DAT **********
ORG {AC202A11-75BA-4050-9982-88DE033E3DE4}
DAT **********</a:History>
<a:Stereotype>ai_conversation_meta</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o134">
<a:ObjectID>2696C2A5-FCB2-454E-AC49-0FF047543BEE</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {B282F810-812E-425A-BCA2-82791E08212A}
DAT **********
ORG {A904CB93-A62D-4B28-B4E2-6889D11F3D92}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>573292E9-0007-4DE0-903D-CE0FDE8DDE37</a:ObjectID>
<a:Name>会话ID</a:Name>
<a:Code>conversation_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>会话ID</a:Comment>
<a:History>ORG {6ADC7C1E-78F9-4334-9961-B7A785A6B357}
DAT **********
ORG {D1EC3673-FAD7-4FA0-A562-5B6DD1B325BC},{DCCABB93-E754-4465-BAD5-492DE81075E9}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>DF65AE6E-0F9F-4BDE-9E01-E22780C2557F</a:ObjectID>
<a:Name>键</a:Name>
<a:Code>meta_key</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>键</a:Comment>
<a:History>ORG {765C7036-E88A-435F-A4EC-99C325170F76}
DAT **********
ORG {33AFDADE-C7FF-46DA-B69B-29CBC6038958}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>643C0CDC-67C6-4AC8-8FF3-7D0D2D4599F1</a:ObjectID>
<a:Name>键值</a:Name>
<a:Code>meta_value</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>键值</a:Comment>
<a:History>ORG {8C941C7E-8504-4376-928F-280E3DEE0BC7}
DAT **********
ORG {688ACF4E-4273-4088-AF3C-D9639BA69972}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>4BBB6DD3-2E08-4D29-803A-AF76BEA42BE0</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531392</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>用户ID</a:Comment>
<a:History>ORG {15ED785B-78C0-4E8E-A574-0FE6F082EFD8}
DAT **********
ORG {CF2DFE55-28C7-41A2-945E-DDBBE9004EE0}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>C51201ED-8F5C-40F9-A05A-42B5D25B63A0</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {9653812E-617C-44FF-806C-A99AC71C0220}
DAT **********
ORG {A4292EAB-7B50-4249-83C0-FF71FB68D355}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o140">
<a:ObjectID>02FE80C1-DC97-47C8-9415-2E7B1565E9CE</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {DC4E4382-48A4-4900-BCB9-EFCAA2A6E2BE}
DAT **********
ORG {F5FAD98C-33B1-48A3-80A0-13446DDDA36B}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>BCC25130-973C-42D9-80B0-0B55F5B4781B</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {376FCF1F-5CC5-4EA5-90AD-993EDBBCEB69}
DAT **********
ORG {0894F7C2-DA6E-4AF3-9DD7-B4331921A230}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>739A9C77-B3A0-4BD7-A65E-C682703B73DB</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {E4674AF3-B494-4600-9B45-AF2F0591F829}
DAT **********
ORG {5279C794-8458-4B5A-B361-9294469D9D68}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>297F0C6C-0BCC-4DA7-806B-105A88F1BC6E</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {F6A24598-172E-4517-B8F6-9C6ECA77F0F3}
DAT **********
ORG {8B40B85B-BB9E-4D05-80E3-95736E6D09A7}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>E42CAAAD-52FC-484D-BAD0-B2D0EB74EF43</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {3646F573-0DE4-40BC-A41D-6F7AFE381FCF}
DAT **********
ORG {EFF49C1C-7649-4666-8528-24C27974EDED}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>05084C2F-1EC0-47E7-A081-9CE4BA870764</a:ObjectID>
<a:Name>备注</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>备注</a:Comment>
<a:History>ORG {E2B269EB-7899-49D7-8F7E-77025418108D}
DAT **********
ORG {E5095720-0973-46CE-9B3C-4FC9DCA54442}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>584E8CC2-8C77-4052-B1D5-F306B64E126E</a:ObjectID>
<a:Name>排序</a:Name>
<a:Code>sort</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>排序</a:Comment>
<a:History>ORG {97B8A7FB-F55E-421C-9C09-FAF813F4CB0A}
DAT **********
ORG {9272F529-506D-479C-A93B-CBA555FEFF40}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>E032DEB4-D805-4D4A-8B66-E9A2A573227C</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {CBAA3491-AE78-4D77-8A58-5B48B1D21871}
DAT **********
ORG {C134C209-BF49-4B8B-B608-218B0D384494}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>179E940F-18D0-4621-AFDC-312B39F0C9BA</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {4E10A397-613A-40D2-97AD-37E998F2FF16}
DAT **********
ORG {AAC918F5-9CB6-4BFE-BABB-A31FA80CAEC6}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o149">
<a:ObjectID>0CA1B211-5DEA-4AC6-A75E-C7EC8391C5A9</a:ObjectID>
<a:Name>pk_ai_conversation_meta</a:Name>
<a:Code>pk_ai_conversation_meta</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {C54F254C-A18E-4FB5-B4C9-F0A7D3F577B6}
DAT **********
ORG {A8FE289F-2F89-4FE2-B155-EAD8C5A3B691}
DAT **********</a:History>
<c:Key.Columns>
<o:Column Ref="o134"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o150">
<a:ObjectID>B72A26B8-EB06-43E8-8271-E27986FA2707</a:ObjectID>
<a:Name>ai_conversation_meta_PK</a:Name>
<a:Code>ai_conversation_meta_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o149"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o151">
<a:ObjectID>2EA183EE-9D86-4517-90BC-B36B84E55ABA</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o134"/>
</c:Column>
<c:Elements>
<o:Column Ref="o134"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o152">
<a:ObjectID>9ECBB09E-EA03-4AA4-9DC4-ECD625D79D29</a:ObjectID>
<a:Name>Relationship_24_FK</a:Name>
<a:Code>Relationship_24_FK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o25"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o153">
<a:ObjectID>DF5E3E90-2742-42A5-ACBA-2E54DCBA4D77</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o135"/>
</c:Column>
<c:Elements>
<o:Column Ref="o135"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o149"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o55">
<a:ObjectID>A722DEE4-69D7-46D1-9187-1B69CE0E6401</a:ObjectID>
<a:Name>记忆访问记录表</a:Name>
<a:Code>ai_memory_access_log</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆访问记录表</a:Comment>
<a:History>ORG {3ED658CD-56FD-461E-A9F9-CDF1DBD3CCF8}
DAT 1750296238
ORG {5BF36334-323A-4B8D-A831-C63EA8AB8C24}
DAT 1750293620</a:History>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o154">
<a:ObjectID>E3FD1226-5F99-49DD-9B1E-29C2BE543772</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {8C62CF43-9496-43AF-BC0C-D8CC59F4D37B}
DAT 1750296238
ORG {C8AD21E1-C81A-4EAD-8AAE-95A682F1E3F5}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>ADD3E1D3-84DB-499B-BCF8-160B24E417A9</a:ObjectID>
<a:Name>记忆实体</a:Name>
<a:Code>agent_memory_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆实体</a:Comment>
<a:History>ORG {9927DEDA-19D3-41DD-9737-09B9885A304A}
DAT 1750296238
ORG {CE07B8F5-FF6F-469A-99D5-44BC0888C19F},{A8B45CA0-319E-4B90-87E7-70C4E0DAA9E1}
DAT 1750293620
ATT NAME
ATT CODE
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>CB122BCD-9DD5-4E8B-AB22-EB50CA4422AA</a:ObjectID>
<a:Name>访问时间</a:Name>
<a:Code>access_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>访问时间</a:Comment>
<a:History>ORG {9BFDE2EE-0EB2-4303-8F34-E287DEB72DA0}
DAT 1750296238
ORG {EC148930-70EC-4FCF-AADC-C27CEB05D56E}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>420748B7-17D6-4C50-8F2A-2DAC3EE8A7B0</a:ObjectID>
<a:Name>访问类型-1-READ|2-UPDATE|3-DELETE</a:Name>
<a:Code>access_type</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>访问类型-1-READ|2-UPDATE|3-DELETE</a:Comment>
<a:History>ORG {8E8E2E7D-11C7-44DB-B8DF-C62924333778}
DAT 1750296238
ORG {C3601DB6-61ED-4163-AAAF-4AC31AE1546D}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>5CA01696-5204-4B60-970F-91DA2F77B7D0</a:ObjectID>
<a:Name>访问者</a:Name>
<a:Code>accessed_by</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>访问者</a:Comment>
<a:History>ORG {54A6BDC4-F9E1-4436-8A2A-552A06AF01FB}
DAT 1750296238
ORG {BF7A95F8-92F9-4D44-9EAE-FE8801394A15}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>ABB75265-3116-4BD1-81D5-35A83E66956B</a:ObjectID>
<a:Name>查询内容</a:Name>
<a:Code>query_context</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>查询内容</a:Comment>
<a:History>ORG {09753963-2F6E-4E61-9FD4-BC3646AEDCA8}
DAT 1750296238
ORG {E51EF393-9BE9-47CF-9646-A1A87346F9C4}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>4555BAA9-01DA-4601-8B56-7480AF62F2B6</a:ObjectID>
<a:Name>元数据-JSON格式</a:Name>
<a:Code>metadata</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>元数据-JSON格式</a:Comment>
<a:History>ORG {18A0130C-44C0-4D0C-B4EB-486B83355621}
DAT 1750296238
ORG {AC2408D7-089D-4B23-97ED-0975713FFFCE}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>1B74B93C-E9A4-42DA-A9C2-89FD97739A41</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {65DF0977-787F-46D0-8207-4CAF62090CDB}
DAT 1750296238
ORG {C6D2A1F9-A2F4-44F0-8A0E-5EBE42ED2FA8}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>E420EA47-027B-4D4A-B0E9-A8DCFDB67282</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {D34E8E90-62A0-4CFC-B9CC-16E33D02D521}
DAT 1750296238
ORG {45F420EA-FACE-419F-BB99-FE53D219E189}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>0CC134F1-1103-439E-BE59-1A00BC5126E5</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {707F7394-061B-4108-AECD-6FB00CAA80E9}
DAT 1750296238
ORG {DE7EF938-89C1-49A8-B55D-7DEBB93FE8BB}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>A3D13187-1C79-4814-A99A-9D0C5D1CA41F</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {A2A82B55-230F-4CD2-A72C-38F15D7F0F93}
DAT 1750296238
ORG {B7238E33-53C3-4CEA-9317-746C6182B908}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o165">
<a:ObjectID>46A931E9-5642-47E4-AFAD-1107FAC798EB</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {CFC8F1B2-D363-4F2F-9912-164D0C934879}
DAT 1750296238
ORG {24F2B06A-8FA1-4B46-9AA0-5BC6CEE8C6B8}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>0B8F5CC8-1ABB-4299-8B6E-442F54F33EC0</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {287496C2-48EC-431C-A2A7-F6111D0EDD4E}
DAT 1750296238
ORG {574113DF-75FB-4FE7-A2CE-31538CBD7884}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>538A652F-4B38-4B7A-9EB6-8B4EA696FF44</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {B2E90DBC-CFEC-415B-90F6-DCC2E59C7931}
DAT 1750296238
ORG {60FCF6CA-BA5A-44CD-817D-2F39F50A32DF}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>4E4FEA16-5982-45DB-AFA1-2FA95A6F6EF9</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {D9284EDE-0ADB-4E6B-BE57-4A153D818FCD}
DAT 1750296238
ORG {35B7E6AC-DF36-4852-888C-7FA675AC0BAF}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o169">
<a:ObjectID>E749904A-EB5A-469F-8B8E-BB45B8A598B5</a:ObjectID>
<a:Name>pk_memory_access_log</a:Name>
<a:Code>pk_memory_access_log</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {CD8D0BD6-7E81-45C7-B681-56474A745F4F}
DAT 1750296238
ORG {63A9B492-4FED-4964-9E54-0EFA7FDB961D}
DAT 1750293620
INS COLNCOL {8C62CF43-9496-43AF-BC0C-D8CC59F4D37B}</a:History>
<c:Key.Columns>
<o:Column Ref="o154"/>
</c:Key.Columns>
</o:Key>
<o:Key Id="o170">
<a:ObjectID>17E5D32F-EFD4-4E47-8E11-935CB38D9DFE</a:ObjectID>
<a:Name>idx_memory_access_log_access_time</a:Name>
<a:Code>idx_memory_access_log_access_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {19E21D08-D123-4F34-A9B0-95953575D719}
DAT 1750296238
ORG {A3609C3C-ACC9-4B27-9382-E38546945D23}
DAT 1750293620
INS COLNCOL {9BFDE2EE-0EB2-4303-8F34-E287DEB72DA0}</a:History>
<c:Key.Columns>
<o:Column Ref="o156"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o171">
<a:ObjectID>A5F28CC9-4B21-46D8-980B-876F4F3F6729</a:ObjectID>
<a:Name>ai_memory_access_log_PK</a:Name>
<a:Code>ai_memory_access_log_PK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o169"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o172">
<a:ObjectID>FF0732A6-162D-4678-B9A6-00502D0B1538</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o154"/>
</c:Column>
<c:Elements>
<o:Column Ref="o154"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o173">
<a:ObjectID>0420955E-6E5C-4DF5-AF0F-E569ECE7A0EE</a:ObjectID>
<a:Name>Relationship_13_FK</a:Name>
<a:Code>Relationship_13_FK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o32"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o174">
<a:ObjectID>BD2A31AD-862F-45D5-84E9-519638DD9A9F</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o155"/>
</c:Column>
<c:Elements>
<o:Column Ref="o155"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o175">
<a:ObjectID>96E9FCF0-A05F-4BB7-9C85-C0BE7B8C9E0D</a:ObjectID>
<a:Name>ai_memory_access_log_AK</a:Name>
<a:Code>ai_memory_access_log_AK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o170"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o176">
<a:ObjectID>7BCEE64E-50C8-4DC9-BE0C-F0D31B14B674</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o156"/>
</c:Column>
<c:Elements>
<o:Column Ref="o156"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o169"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o54">
<a:ObjectID>C5B82172-9E24-4400-A08C-48A911FEEDB2</a:ObjectID>
<a:Name>记忆分类</a:Name>
<a:Code>ai_memory_category</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆分类</a:Comment>
<a:History>ORG {F0D41CE8-F08B-47A4-B66E-DBE535FCB816}
DAT 1750296238
ORG {BCA29221-E835-4457-9072-9949E728EC9E}
DAT 1750293620</a:History>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o177">
<a:ObjectID>EC503D48-BDB1-44FB-BAC4-6603C8D14CA4</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {20238CDC-909F-436D-877D-DD3603B53421}
DAT 1750296238
ORG {6DFCAA08-4242-4226-BB2D-02FF29D5F72E}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>6AC27997-AE4F-423D-99B4-CDB8A99F057C</a:ObjectID>
<a:Name>分类名称</a:Name>
<a:Code>category_name</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>分类名称</a:Comment>
<a:History>ORG {2C6B2063-527E-4FE4-AAA7-460EB81A587E}
DAT 1750296238
ORG {030B5E6C-6291-4728-89F0-9BEA86C5B18A}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>74C56B32-00CC-4D25-B21C-87254C2D69FD</a:ObjectID>
<a:Name>上一级</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>上一级</a:Comment>
<a:History>ORG {F9C3CB11-EB7C-4089-8745-3E26EEC23F97}
DAT 1750296238
ORG {23512073-CF4C-4162-B852-80B386460213}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>97450CF3-A117-49CC-B8EA-19BF18359EBD</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>description</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>描述</a:Comment>
<a:History>ORG {4D9E4B18-FF97-48E6-B1FB-C065B815272E}
DAT 1750296238
ORG {F2F33E0B-FBBA-4B9C-969A-071577BE48AD}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>39E66254-FE14-4E2F-A12B-B48E98BB0715</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {F12C2D2E-E835-4CFD-8607-0153883B43D2}
DAT 1750296238
ORG {5E1D716C-8812-462A-B54D-BBAE3312A4C8}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>6A025CA5-DC11-43A1-9AF2-9626643F7ED5</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {5F69847E-A6B5-4CAB-AF05-A3A6905807DE}
DAT 1750296238
ORG {6DB6DA76-FDFE-4888-A288-6CCCB3EDF3A0}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>5F0BE626-3E9D-4D6A-A669-F20D079B602D</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {705FFDC5-CD2C-406B-828E-96AE3A23D227}
DAT 1750296238
ORG {CE6281CD-2F26-4EDC-97D3-3254D51D697B}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>E069A932-2367-4231-8A6E-AB7330749220</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {06EABA59-0F0C-4960-B739-151500297B69}
DAT 1750296238
ORG {F857C08C-325C-44CB-AD41-8780B5F35325}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>DE594D9D-98C8-4776-8A6A-212248AFEBFB</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {EF8B6077-038E-4002-8EFB-0A2235433B15}
DAT 1750296238
ORG {5D3057F5-67D0-4B31-94A7-AA7B5F9085A0}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>91AC8D60-D9CB-4BD0-8005-1A43CFB968B8</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {ACFC8E89-6879-48A1-A3D1-2CF5AD42536D}
DAT 1750296238
ORG {C2BC0A24-BE19-44CB-8088-75476E7C9DB0}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>A19E094A-FAD3-4DF8-9618-041829BF3829</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {D45B7690-78E2-46ED-9648-DEF449A31BAB}
DAT 1750296238
ORG {CF65DA67-4567-43FF-9400-E8AC566A802D}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>8A5E26DE-139E-4B30-AE0F-1B91393600E4</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {DAAF04F3-E820-45A8-83BA-4CC1E8622B33}
DAT 1750296238
ORG {DABAC7BD-BD62-46C3-890C-D966B71795CF}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o189">
<a:ObjectID>ECA60751-DFCB-4466-B66C-03FF28B1B13B</a:ObjectID>
<a:Name>pk_memory_category</a:Name>
<a:Code>pk_memory_category</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {0D9A0CCF-621A-47CD-AE21-F73BF49DCCA9}
DAT 1750296238
ORG {7030CC90-EDE1-4C4F-97B0-5722DF63F6ED}
DAT 1750293620
INS COLNCOL {20238CDC-909F-436D-877D-DD3603B53421}</a:History>
<c:Key.Columns>
<o:Column Ref="o177"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o190">
<a:ObjectID>959A010B-EFD2-42D5-B153-53F4F8BD94F7</a:ObjectID>
<a:Name>ai_memory_category_PK</a:Name>
<a:Code>ai_memory_category_PK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o189"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o191">
<a:ObjectID>00FE971A-DB15-4CFE-B7D9-CF72261597B5</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o177"/>
</c:Column>
<c:Elements>
<o:Column Ref="o177"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o189"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o47">
<a:ObjectID>597A0C66-B128-4B08-919F-1E5E097382EA</a:ObjectID>
<a:Name>AI用户评价结果</a:Name>
<a:Code>ai_chat_comment</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>AI用户评价结果</a:Comment>
<a:History>ORG {B93AE969-D37F-4338-8050-22ABC50847CA}
DAT **********
ORG {98A6EDED-291E-4982-A398-FCC13AE60351}
DAT **********
MOV COLNCOL</a:History>
<a:Stereotype>ai_chat_comment</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o192">
<a:ObjectID>8BA0D8E4-C7EB-44C6-9EF1-DA7230B3C14B</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {B291D3ED-1CDC-467E-9101-6103FE9B66BA}
DAT **********
ORG {59F72F69-3F8E-45F1-B4CA-A34BA33A30FA}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>27E0DB2B-861F-4701-BA2F-31E0F21572DB</a:ObjectID>
<a:Name>会话ID</a:Name>
<a:Code>conversation_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>会话ID</a:Comment>
<a:History>ORG {E116EABB-6F9A-457D-BD5C-BD6079F54218}
DAT **********
ORG {D1EC3673-FAD7-4FA0-A562-5B6DD1B325BC},{34193417-1724-49E0-9CF7-8A4676E6C7FC}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>B2552AE8-877A-4BD0-B661-E75C34F7A514</a:ObjectID>
<a:Name>会话内容</a:Name>
<a:Code>content_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>会话内容</a:Comment>
<a:History>ORG {AFB90A81-DDE8-4828-84F1-FCBE842D2715}
DAT **********
ORG {8435A027-6923-4738-8F1D-87A709CF9A2D},{C657EE89-1075-41F6-AFFF-6FDF801262B5}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>D1204835-1B5D-43F8-98C9-CADC6C04859E</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531392</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>用户ID</a:Comment>
<a:History>ORG {4B642694-2FC0-46D7-AB76-EDF6F7F7F843}
DAT **********
ORG {81EE5E3F-7A56-49B3-B853-CA5ABE0D66AB}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>3769A8DA-9760-4D07-960E-1EDF7B3C8E85</a:ObjectID>
<a:Name>模型类型</a:Name>
<a:Code>model_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>模型类型</a:Comment>
<a:History>ORG {0E525FDB-C1BD-43C9-A234-536D6893B7D2}
DAT **********
ORG {8A677B6B-8D62-47D2-9857-35A2FBC0D3F5}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>DF7F4305-E8BC-427C-AC34-13BD85CDAF60</a:ObjectID>
<a:Name>是否喜欢，默认喜欢1</a:Name>
<a:Code>is_like</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531125</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否喜欢，默认喜欢1</a:Comment>
<a:History>ORG {4DD2ABF6-45F1-4D72-8EB5-A613190DF107}
DAT **********
ORG {DD2EFD33-43C4-42D8-921C-ED7EE8393358}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>084CB4D2-E6B2-465E-8217-E2CECEADCF82</a:ObjectID>
<a:Name>默认评论</a:Name>
<a:Code>default_comment</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>默认评论</a:Comment>
<a:History>ORG {A823A20B-63AF-4A34-8BF1-D2734CF078CA}
DAT **********
ORG {F675C57B-BDFB-44D3-B3B4-57CABA769CD1}
DAT **********
ATT LABL</a:History>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>1A25A3E5-5B7A-421C-AF99-9EEAA371138B</a:ObjectID>
<a:Name>是否采纳，默认否0</a:Name>
<a:Code>is_accept</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531125</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否采纳，默认否0</a:Comment>
<a:History>ORG {867A2647-7885-4850-A599-4087B1123450}
DAT **********
ORG {15224A46-6948-4FA3-95C1-09C524CAEC0E}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>B8091A09-6242-4890-9379-93EE7708EFB1</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {9E8C0659-ADD1-4D5B-B043-EF485BE6A0C4}
DAT **********
ORG {78874E41-57F2-43AB-8F7F-A21417051662}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o201">
<a:ObjectID>AF711533-3EB5-4F8D-A785-754CA464D093</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {A2221479-3627-4125-BD60-5312CAAD7BB3}
DAT **********
ORG {D30E3BDD-A25F-4FFF-90A2-E649819CB336}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o202">
<a:ObjectID>4006ACDC-D789-4167-AA0E-A4A0EC19BDB5</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {29CE0C70-B182-432B-A863-637C795DE99C}
DAT **********
ORG {204CD1F5-6B18-485D-862F-4B03B05F1663}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>E9FA754D-BE22-474B-94F3-C1580DE36AB1</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {41B144DB-F680-4D7C-8FD0-14BF16FE8F95}
DAT **********
ORG {F521C766-8EF2-454D-9269-23403C763D7F}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>67B58CB5-62A3-4967-948C-305CC353A83C</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {38E3C707-E78E-4465-BB9C-22C0F4F18CE1}
DAT **********
ORG {94F10463-5B64-45E9-9198-AC9088F63502}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>C85C4871-9E98-4AFB-932C-310F1F8DC556</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {0C190872-EBB4-4C4C-BEDC-672613560D61}
DAT **********
ORG {C8992CAB-DBA9-45BE-8402-8E4D2CF688D8}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o206">
<a:ObjectID>1F653911-71D2-47F1-A787-39F0F61F1E1F</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {1717CBCC-84C7-49DC-B75D-0964D29216BE}
DAT **********
ORG {99AFFEED-9435-4283-9888-22EADB6746A0}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o207">
<a:ObjectID>EE5DD8E9-22A7-4C9B-ABD2-991C8C131BE1</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {25258386-0214-4DED-A852-494030010681}
DAT **********
ORG {175B3CF8-D511-4C63-AEB9-C17C4A3DB329}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o208">
<a:ObjectID>3EE77E66-6183-459A-AD00-16AA9739AE26</a:ObjectID>
<a:Name>pk_ai_chat_comment</a:Name>
<a:Code>pk_ai_chat_comment</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {93A4EF19-0EEA-4CC9-8CF6-DDD42B64B843}
DAT **********
ORG {0F4F67BA-230B-4C2C-87F4-4A27EB881DA3}
DAT **********</a:History>
<c:Key.Columns>
<o:Column Ref="o192"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o209">
<a:ObjectID>B40C3E64-6822-4250-9656-F7533816181F</a:ObjectID>
<a:Name>ai_chat_comment_PK</a:Name>
<a:Code>ai_chat_comment_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o208"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o210">
<a:ObjectID>BFACCD63-1E6F-4B28-AE41-734E70BBE876</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o192"/>
</c:Column>
<c:Elements>
<o:Column Ref="o192"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o211">
<a:ObjectID>CD02ACB8-A6A5-4BDC-A69F-639A5BE72085</a:ObjectID>
<a:Name>Relationship_20_FK</a:Name>
<a:Code>Relationship_20_FK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o15"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o212">
<a:ObjectID>3612A7EE-A59A-4200-BFAE-507C1BC8BED6</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o193"/>
</c:Column>
<c:Elements>
<o:Column Ref="o193"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o213">
<a:ObjectID>58E76E58-3393-4378-8998-65F581F52911</a:ObjectID>
<a:Name>Relationship_22_FK</a:Name>
<a:Code>Relationship_22_FK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o20"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o214">
<a:ObjectID>2B632F7A-7A72-45A7-A4F2-D19C1A4251FD</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o194"/>
</c:Column>
<c:Elements>
<o:Column Ref="o194"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o208"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o52">
<a:ObjectID>3CE8CC16-281D-4963-8A29-62EDF01744D5</a:ObjectID>
<a:Name>记忆体元数据</a:Name>
<a:Code>ai_agent_memory_meta</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆体元数据</a:Comment>
<a:History>ORG {2F3E2C1E-A2F8-4889-BAF9-8BD86CF19BBE}
DAT 1750296238
ORG {DB0163E8-7301-4A90-BF44-2A3BE2EB7FFC}
DAT 1750293620</a:History>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o215">
<a:ObjectID>44749AC1-6F4E-4C5C-8AD0-D22D71CF5FE7</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {F421D932-7502-41B5-BC32-CCD5C6D86C7D}
DAT 1750296238
ORG {04E1A25D-009D-41A3-95C2-45A468EB4FFA}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>6C7952A6-91FF-47C0-9B8D-8383A73EBB1E</a:ObjectID>
<a:Name>记忆实体</a:Name>
<a:Code>agent_memory_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆实体</a:Comment>
<a:History>ORG {C2D2D7A8-4658-46B0-BF5F-4342C887A5D9}
DAT 1750296238
ORG {CE07B8F5-FF6F-469A-99D5-44BC0888C19F},{23FF23B9-3D24-4C24-A678-56500409EDD1}
DAT 1750293620
ATT NAME
ATT CODE
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>C6F974F4-A0CA-42A3-BCF9-439E7BF7E78C</a:ObjectID>
<a:Name>记忆key</a:Name>
<a:Code>memory_key</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆key</a:Comment>
<a:History>ORG {25F25239-ECAB-459D-B00A-58B998F6A106}
DAT 1750296238
ORG {37462E25-F028-4518-9761-A32228905446}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o218">
<a:ObjectID>F53739DD-4E69-43CC-9812-E7556A537265</a:ObjectID>
<a:Name>记忆value</a:Name>
<a:Code>memory_value</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆value</a:Comment>
<a:History>ORG {E80633C0-341C-4924-88C9-3C4322C23E36}
DAT 1750296238
ORG {07CAD4DA-DD64-4582-BC7F-E4DCACAA368F}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o219">
<a:ObjectID>C5AB48A5-F5F7-4212-B12F-A72D2BD6B05E</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {BB389641-F8EE-4C09-B56B-ADA60D081724}
DAT 1750296238
ORG {71A4A547-27B0-4FE9-A930-C3613CC5E882}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o220">
<a:ObjectID>9810CF09-C683-4B60-98F8-D45B484A318C</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {725F23CC-3F89-4375-B822-515CD7D0787F}
DAT 1750296238
ORG {647B249E-7C83-4584-A38D-F2E88252C95E}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o221">
<a:ObjectID>123D6AC3-0BDA-478B-8AC8-0C729F0023E1</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {774DF7ED-488B-4CD0-89EF-3A6BC0BE340C}
DAT 1750296238
ORG {53DB0100-9DC5-4022-AFF8-449565D9F6F6}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o222">
<a:ObjectID>B8D6F5C0-F804-403B-AA49-3739EB4452E7</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {B3FD7D8C-6B1D-4DFA-B0AB-C9DA98B35670}
DAT 1750296238
ORG {DB242CD2-6C20-46DE-99CF-65E925A2B8E0}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o223">
<a:ObjectID>CFDBE824-8F37-4663-85F6-7BFAFF1B2C72</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {5C2A7A4C-1451-4133-9D5A-D6F88ADD35D7}
DAT 1750296238
ORG {D68B3F2E-8248-415B-A429-64A3E091E6BC}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o224">
<a:ObjectID>B903C03D-90C8-4A0E-8FE6-382B049F909E</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {2DCDC27F-E49C-4DA2-B68D-ED47DBD3C006}
DAT 1750296238
ORG {5D67B482-0AAF-4671-8FBF-E421538F7BE3}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o225">
<a:ObjectID>0B16174A-8C7E-4E7B-9D2E-97F2A7B0BD53</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {7EBB3D66-C647-4065-9B98-DA52ED7004B1}
DAT 1750296238
ORG {48D349D9-67A8-4CC6-A37B-8EE2A11B0E31}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o226">
<a:ObjectID>9D801373-8ABC-42F4-9D44-89E1935F17F8</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {92E3897F-8BEE-4D99-881D-86FCBACA0631}
DAT 1750296238
ORG {3F6CD6E6-003E-41A7-A85E-82C283B5C3DB}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o227">
<a:ObjectID>BA54CBF4-69A2-4AAE-B8B0-7CBE1F122326</a:ObjectID>
<a:Name>pk_agent_memory_meta</a:Name>
<a:Code>pk_agent_memory_meta</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {E94F015D-A50C-4A46-A39B-9B38BA6D702F}
DAT 1750296238
ORG {B59ADDD9-BFDD-47F1-A6ED-C1DC570ED784}
DAT 1750293620
INS COLNCOL {F421D932-7502-41B5-BC32-CCD5C6D86C7D}</a:History>
<c:Key.Columns>
<o:Column Ref="o215"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o228">
<a:ObjectID>6C369536-8D4B-4571-9D69-988673073D19</a:ObjectID>
<a:Name>ai_agent_memory_meta_PK</a:Name>
<a:Code>ai_agent_memory_meta_PK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o227"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o229">
<a:ObjectID>C6719FA4-A366-44B3-A102-5127B82F9977</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o215"/>
</c:Column>
<c:Elements>
<o:Column Ref="o215"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o230">
<a:ObjectID>8DC27982-54B6-4CFD-B3D8-AF1FD1FA2D56</a:ObjectID>
<a:Name>Relationship_12_FK</a:Name>
<a:Code>Relationship_12_FK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o29"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o231">
<a:ObjectID>96F1798E-6983-4369-8C51-4843B2C334C3</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o216"/>
</c:Column>
<c:Elements>
<o:Column Ref="o216"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o227"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o51">
<a:ObjectID>20EC7AB8-9B64-4A57-BA22-2CA7857101B5</a:ObjectID>
<a:Name>记忆实体</a:Name>
<a:Code>ai_agent_memory</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆实体</a:Comment>
<a:History>ORG {185E10A6-356D-42CF-BE05-59CB48E58614}
DAT 1750296238
ORG {3761CB9C-D9E4-419D-8FB6-FB12C68BB314}
DAT 1750293620</a:History>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o232">
<a:ObjectID>E7803607-0DAD-46E9-9B9A-17D7CDA3253D</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {F398C037-12EA-4F06-8C37-2E2640EB9ADE}
DAT 1750296238
ORG {CE07B8F5-FF6F-469A-99D5-44BC0888C19F}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o233">
<a:ObjectID>13877869-3165-4E45-B5E9-1DCC1A449869</a:ObjectID>
<a:Name>会话ID</a:Name>
<a:Code>conversation_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>会话ID</a:Comment>
<a:History>ORG {710815E0-7D67-4B4A-AAC8-34FDFAC065A2}
DAT 1750296238
ORG {AEDF35DF-368D-49A0-95B6-477A75C5FB56}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o234">
<a:ObjectID>EA3A64AD-0954-421F-9DBB-DABCAA6221AB</a:ObjectID>
<a:Name>角色</a:Name>
<a:Code>role</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>角色</a:Comment>
<a:History>ORG {25BAA069-E919-4FDA-8323-565AE4821147}
DAT 1750296238
ORG {D87F9438-AED6-4538-A6A9-7DBBD25C6CC2}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>DB5DDEFA-68D0-4D58-8EFA-72B8F2CFD2F8</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>content</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>内容</a:Comment>
<a:History>ORG {E4C0BF35-33BC-46FB-A718-5F6ADAFD68EE}
DAT 1750296238
ORG {5B8DE416-4C91-4C4E-BEC9-3693389EF9A5}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o236">
<a:ObjectID>2E5F4486-02BE-44EC-864F-C52BC06B5303</a:ObjectID>
<a:Name>重要性评分</a:Name>
<a:Code>importance_score</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>重要性评分</a:Comment>
<a:History>ORG {177C708E-1ED5-4447-8C45-23C9FB8096F4}
DAT 1750296238
ORG {05C5E54B-663B-4F66-A8D7-8803142510E3}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>double</a:DataType>
</o:Column>
<o:Column Id="o237">
<a:ObjectID>E17ACC75-A094-4458-85D4-F6B6B6D18693</a:ObjectID>
<a:Name>相关性评分</a:Name>
<a:Code>relevance_score</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>相关性评分</a:Comment>
<a:History>ORG {49331C1A-9ABB-4C1B-B5A4-687E49738481}
DAT 1750296238
ORG {794E9F94-AAD8-49F8-8C1B-53AB4DAF489A}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>double</a:DataType>
</o:Column>
<o:Column Id="o238">
<a:ObjectID>AFB9EBB7-4AF0-47FF-B7C0-2895379AC1B5</a:ObjectID>
<a:Name>是否关键</a:Name>
<a:Code>is_critical</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否关键</a:Comment>
<a:History>ORG {4E63A0CE-84AE-421B-A2DE-6E32CC865D9C}
DAT 1750296238
ORG {B7CEFD0B-C978-4760-87CD-AC0DA783D9C9}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o239">
<a:ObjectID>B1460A42-2600-4A95-B899-EA6059036E5E</a:ObjectID>
<a:Name>记忆类型-0会话|1知识库|2事件</a:Name>
<a:Code>memory_type</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>记忆类型-0会话|1知识库|2事件</a:Comment>
<a:History>ORG {621109A5-1083-4235-875F-1B2B74964FB2}
DAT 1750296238
ORG {B8034EB9-78F9-4422-902D-21A48DE34D2B}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o240">
<a:ObjectID>1FA0D1DB-738D-4C70-8E2D-8113E5554BB1</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {5284C13A-09A5-4C5F-BBCE-045A5CCFAD15}
DAT 1750296238
ORG {3B4862DD-2674-4982-9B21-64F08B967735}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o241">
<a:ObjectID>323DF547-CABE-42A9-AF50-37DC0BA1F6D3</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {BB0C3AD0-AFF6-40E5-B5FE-254C6838178D}
DAT 1750296238
ORG {BE34B69D-F2DE-4E4D-B58A-8D9CC3621656}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o242">
<a:ObjectID>D796E630-1861-4827-B380-32D82507A28E</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {E8B5987D-1FFE-4A01-81A8-B48D7B1FF4ED}
DAT 1750296238
ORG {09E6CE3F-4D73-4E5A-870A-E3393DD72DA2}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o243">
<a:ObjectID>9514D3E0-4B68-42B2-8352-0A5D64893EF8</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {A532CDC9-C5DA-4B74-BFA7-5C7D288358AD}
DAT 1750296238
ORG {4F0FC6ED-1DB3-4867-8A3B-8C8A7E36E3D1}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o244">
<a:ObjectID>6C211CA4-124F-425E-989C-5A664D797F1F</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {B49197FA-D9A2-4272-B631-BDECC12DDA4C}
DAT 1750296238
ORG {D616DBEA-E5C2-4E2E-B48A-92ABECCB2260}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>DD8B67B6-B2CF-4783-B4F9-E930FDB2A972</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {9016B02C-BE33-4FB1-A7E6-8D8FA925FBDA}
DAT 1750296238
ORG {EAF2C3DA-329F-494F-AFB4-3331A426F79F}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>7BFC3A6D-7D3F-4579-8485-FC20ADD0F0A6</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {3B0C9FE4-252C-442C-90E7-5C2BA310670B}
DAT 1750296238
ORG {95BBA35D-6FC3-4D4E-A78A-46378D3FEBF2}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o247">
<a:ObjectID>A954FD5F-CB58-4A0B-AD2C-FFDEB9B00945</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {D721B973-AE65-4377-813C-C58CC5147CB9}
DAT 1750296238
ORG {C8E5BB8A-7291-42F6-93A4-CA909A3FF3B0}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o248">
<a:ObjectID>81E2FF9D-339B-4ECD-BA48-348E5AA9F28F</a:ObjectID>
<a:Name>pk_agent_memory</a:Name>
<a:Code>pk_agent_memory</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {A1743E3B-E8A7-4E68-A794-373294DECF4A}
DAT 1750296238
ORG {7D08939D-B0BF-4B2A-ABAB-B399403242AA}
DAT 1750293620
INS COLNCOL {F398C037-12EA-4F06-8C37-2E2640EB9ADE}</a:History>
<c:Key.Columns>
<o:Column Ref="o232"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o249">
<a:ObjectID>A1A5B11E-D4FC-463E-BC42-B5A087401DCA</a:ObjectID>
<a:Name>ai_agent_memory_PK</a:Name>
<a:Code>ai_agent_memory_PK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o248"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o250">
<a:ObjectID>5F964F0E-6B93-4ED4-87FB-91459F712C17</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o232"/>
</c:Column>
<c:Elements>
<o:Column Ref="o232"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o248"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o57">
<a:ObjectID>7178A874-2930-440D-B133-DA93F7BDBCCC</a:ObjectID>
<a:Name>提示词变量</a:Name>
<a:Code>ai_prompt_variable</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>提示词变量</a:Comment>
<a:History>ORG {EE0F6857-4AF7-4950-83CC-ABC47E8DF227}
DAT 1750296238
ORG {F54683DE-5E01-4BF1-BCC7-19446990F85E}
DAT 1750293620</a:History>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o251">
<a:ObjectID>25B79E78-1321-4E97-9E78-2CE2512514BA</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {5128463E-F916-4EC6-B887-526949EE5BD9}
DAT 1750296238
ORG {23E1FD60-D652-49DA-8B55-E5EB083CC53F}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o252">
<a:ObjectID>0C2D5219-5E63-47A5-8126-1A337D2AD868</a:ObjectID>
<a:Name>提示词模版</a:Name>
<a:Code>prompt_template_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>提示词模版</a:Comment>
<a:History>ORG {BE90A437-5FBA-4671-AAB0-99B75316D206}
DAT 1750296238
ORG {E9420476-D992-4BCB-B2F6-D6FE1F09ADAE},{36C6BFAE-7F99-40F8-90F0-3207CF7C236B}
DAT 1750293620
ATT NAME
ATT CODE
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o253">
<a:ObjectID>0E730FF7-0F8D-4383-9B03-351A60D1366B</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>名称</a:Comment>
<a:History>ORG {C2063963-6A23-4EC1-8895-A7CD10ACE6F3}
DAT 1750296238
ORG {727A032C-4F91-45EA-A85A-43833201C5F7}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o254">
<a:ObjectID>7232BEBC-374B-4DFB-913F-D04ECD2D0A5F</a:ObjectID>
<a:Name>类型</a:Name>
<a:Code>type</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>类型</a:Comment>
<a:History>ORG {30FCDF6D-643D-4C55-BB39-3F6F0749CBD6}
DAT 1750296238
ORG {8AB5A159-2107-48A4-9746-AFE7312B9C16}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o255">
<a:ObjectID>D40EAB13-6F32-462B-9BA0-DFC53EF3D96A</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>description</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>描述</a:Comment>
<a:History>ORG {8BF19ACE-6993-4FA2-9801-E8494C859181}
DAT 1750296238
ORG {9B863D8E-6F80-4C60-9A71-98C47E391126}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o256">
<a:ObjectID>3F9C1FE5-72E7-4270-94A0-88ED03C699F4</a:ObjectID>
<a:Name>默认值</a:Name>
<a:Code>default_value</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>默认值</a:Comment>
<a:History>ORG {EC573415-A69A-4414-95C2-3491F4414188}
DAT 1750296238
ORG {ACDD5D12-CB24-4F55-9E97-A8FAB9B3A85B}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o257">
<a:ObjectID>DF3FCCDF-18BD-4581-AC12-434A3E20BEC3</a:ObjectID>
<a:Name>是否必填</a:Name>
<a:Code>is_required</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否必填</a:Comment>
<a:History>ORG {5FE01A0D-1188-4374-B729-D8144D157CF1}
DAT 1750296238
ORG {6FC72147-BB26-461D-87E6-63EFF6EF9EC1}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o258">
<a:ObjectID>9CEE7D99-C715-4C01-9BCB-3943C7B0B663</a:ObjectID>
<a:Name>允许值-JSON格式</a:Name>
<a:Code>allowed_values</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>允许值-JSON格式</a:Comment>
<a:History>ORG {2B924D76-08EF-4AC3-8F3F-42157C6F4613}
DAT 1750296238
ORG {FE477C2F-9679-4372-A0C9-C3F94467B89E}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o259">
<a:ObjectID>5A55AC3A-D198-4D47-A63D-0A1B1E074463</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {FFCD89E9-B1F0-42CB-B7AA-76E3D26931C3}
DAT 1750296238
ORG {B694D8A3-6CB1-49E5-AB5D-43BCA5F4AF03}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o260">
<a:ObjectID>A6306B55-012F-4D97-9E36-7CE006053D51</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {89D80820-16CD-4891-9A31-6713AD989FAB}
DAT 1750296238
ORG {AC4F5376-5460-48DF-9975-2CDF0EB5FC27}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o261">
<a:ObjectID>11AF3CDE-280D-4858-93D3-64AF68AF5957</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {87C0CBE9-9BA0-4BED-AF33-676C35432964}
DAT 1750296238
ORG {DDDD0661-CBB6-4F43-BF93-8833B649B8A5}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o262">
<a:ObjectID>1285D858-0CA5-4F35-BB68-4BF10DA5A970</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {E2AEDCD6-7335-4F94-9DD7-14F307D272DA}
DAT 1750296238
ORG {CA49CAC7-4FB6-49BB-B5FE-9D235A7BF39E}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o263">
<a:ObjectID>0D9CDDE9-C19C-4571-A8BC-7ADE13C638E9</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {A67E9BB8-0459-474D-907C-D10C9E515C6F}
DAT 1750296238
ORG {3CD94A16-44C1-49C0-A331-ABE5DDE32634}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o264">
<a:ObjectID>A5975B9A-581F-4CD1-9D00-67E422B4DB68</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {13FAD999-026D-41EE-8037-D8E6E48C93AB}
DAT 1750296238
ORG {5A3A8751-8A4A-4ADE-905E-E1E6E8268D03}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o265">
<a:ObjectID>237E5C05-0C9C-4A05-AC25-2871FCA3C6E2</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {0EF92491-4B0B-4988-A841-7E1BBB3D188A}
DAT 1750296238
ORG {13F637ED-2EA2-41DE-A0F7-6F8C3FCC8B24}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o266">
<a:ObjectID>D5612FC2-EAEB-4C4C-9295-FC0A4A17994E</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {1CE9474C-9DFE-414A-A9EA-ED9903B559B5}
DAT 1750296238
ORG {EEAFD0F9-A66D-4C47-85AD-8A690759F2C5}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o267">
<a:ObjectID>9EE5DB08-40B0-408F-A12D-7670BBE752DF</a:ObjectID>
<a:Name>pk_prompt_variable</a:Name>
<a:Code>pk_prompt_variable</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {CE225210-A12B-4EE3-AD9B-D95ABD9BA552}
DAT 1750296238
ORG {FB6CBDDE-F241-490D-82A7-2287D586F69B}
DAT 1750293620
INS COLNCOL {5128463E-F916-4EC6-B887-526949EE5BD9}</a:History>
<c:Key.Columns>
<o:Column Ref="o251"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o268">
<a:ObjectID>B7218665-6205-4C50-9E85-448A24DFAD92</a:ObjectID>
<a:Name>ai_prompt_variable_PK</a:Name>
<a:Code>ai_prompt_variable_PK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o267"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o269">
<a:ObjectID>484A1DDF-8BEB-4CBA-B134-DB911905D19E</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o251"/>
</c:Column>
<c:Elements>
<o:Column Ref="o251"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o270">
<a:ObjectID>892410F5-5DB3-4BED-B77D-3BE1B8394B92</a:ObjectID>
<a:Name>Relationship_14_FK</a:Name>
<a:Code>Relationship_14_FK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o36"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o271">
<a:ObjectID>2D1B0CB5-2EEE-45FF-8A41-24889C88DF65</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o252"/>
</c:Column>
<c:Elements>
<o:Column Ref="o252"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o267"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o56">
<a:ObjectID>604E8643-6900-4CDB-B156-FD3DDAE4511B</a:ObjectID>
<a:Name>提示词模版</a:Name>
<a:Code>ai_prompt_template</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>提示词模版</a:Comment>
<a:History>ORG {C331E112-5B00-4CE6-A71E-FA1F7021147C}
DAT 1750296238
ORG {47ABFC11-0914-48F8-B48D-B6D3787A0B21}
DAT 1750293620</a:History>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o272">
<a:ObjectID>CE26D73B-3AD9-4541-B49C-38C89D6CED61</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {914664EE-51C0-4DE7-96C0-738BC623DD9A}
DAT 1750296238
ORG {E9420476-D992-4BCB-B2F6-D6FE1F09ADAE}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o273">
<a:ObjectID>5E6B7F2C-C410-4364-BA28-FECABA00EBAD</a:ObjectID>
<a:Name>模版名称</a:Name>
<a:Code>tpl_name</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>模版名称</a:Comment>
<a:History>ORG {7A2AE36C-9652-4C8F-BD4C-8DA4E008CA05}
DAT 1750296238
ORG {84B7D014-AB81-4A0B-9F81-6D3BF2962B4D}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o274">
<a:ObjectID>C5E99BBB-50FC-44E6-BCDE-E32663273106</a:ObjectID>
<a:Name>模版描述</a:Name>
<a:Code>tpl_description</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>模版描述</a:Comment>
<a:History>ORG {B2B49715-5FAA-415A-BFE8-1235A6F633C6}
DAT 1750296238
ORG {03F802F6-8B9E-4561-84C8-5DE255BC3D7E}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
</o:Column>
<o:Column Id="o275">
<a:ObjectID>0356EB81-6D67-4838-902E-BF26B6D354CA</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>content</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>内容</a:Comment>
<a:History>ORG {34B24E2F-99D1-4A86-8BB6-19581BE98289}
DAT 1750296238
ORG {57A3F03F-9C1D-49DE-98DE-94497F8F9AE0}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o276">
<a:ObjectID>DA88A98B-4586-4CFC-91CE-12AFA9C7DF9D</a:ObjectID>
<a:Name>作者</a:Name>
<a:Code>author</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>作者</a:Comment>
<a:History>ORG {A85A6488-1E21-4457-B595-A042F39ED3EE}
DAT 1750296238
ORG {B33FA309-37BD-4528-B3F2-8D44F27D8D79}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o277">
<a:ObjectID>48D5C945-F4E2-4B90-BC20-BF9B2E41E68E</a:ObjectID>
<a:Name>元数据</a:Name>
<a:Code>metadata</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>元数据</a:Comment>
<a:History>ORG {63A00F38-F692-477A-9DE9-D21ABCE1A3C0}
DAT 1750296238
ORG {3C1C19E2-792E-4A3F-AE76-70D099BA89F0}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o278">
<a:ObjectID>A989DA60-70AC-4149-86B2-A56AC31FDCC6</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {3983AF93-0D19-4E72-B003-53E49C47B8AB}
DAT 1750296238
ORG {12776D42-781B-4CB3-91D8-05C56383E917}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o279">
<a:ObjectID>4419A076-6501-481D-9640-A20B4BABDA7C</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {6492C78B-7113-4182-B378-A5CB0EAA6C04}
DAT 1750296238
ORG {31063591-DB8B-4A9E-BA41-164CC2375D5E}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o280">
<a:ObjectID>F8D641E9-C6CA-4C2D-924C-CDC3BB0F3B3E</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {C537132E-2465-4A15-B844-2BEB2D546D57}
DAT 1750296238
ORG {B30E2004-8C2C-4327-B2A9-858654316208}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o281">
<a:ObjectID>5CF5E815-B97C-4C6E-B57C-A08EE617659D</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {BDD75549-843C-44A5-9358-2BFE0F3D0E06}
DAT 1750296238
ORG {CB0AAFAE-A1AB-4E07-8E1E-558A0F98FEC2}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o282">
<a:ObjectID>A28072A4-DA4F-4904-804F-EFDE5E9BD5C9</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {977F91F2-B4FE-485B-80D6-17FA33252FBA}
DAT 1750296238
ORG {6E50917D-F3FB-4972-A815-8828C4BB39F9}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o283">
<a:ObjectID>EC5332CC-118E-434D-AB6F-666840AA48B9</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {F33E3B0A-33A6-4707-817A-A9A55DFF3FC5}
DAT 1750296238
ORG {1844801C-B8DA-453B-BE07-97EC891CF2B4}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o284">
<a:ObjectID>026D9C89-20E0-4D92-83A4-CCEE22C9C865</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {33CF2ECF-27BA-46C3-88A1-F8D161CBCE6A}
DAT 1750296238
ORG {6C822F0E-AFEF-406B-9708-9A19964B20E9}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o285">
<a:ObjectID>C7D7B005-48E2-4DEB-AF0E-39726AAAFC0C</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {BAAAC032-5F7C-4277-BB9F-17CBEC40481C}
DAT 1750296238
ORG {B093133F-C7E1-4C0B-A5AA-60A08AE2D662}
DAT 1750293620
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o286">
<a:ObjectID>1B6C3125-7B8A-406A-B844-FEEB7BA02930</a:ObjectID>
<a:Name>上一级</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>上一级</a:Comment>
<a:History>ORG {F8C62F12-9DFC-448B-9BFB-E9940545036D}
DAT 1750296238
ORG {489740E4-2FE9-464F-9DC0-579658AFEFEF}
DAT 1750293620
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o287">
<a:ObjectID>EEECB32F-E6F9-476C-A185-6F16826F7032</a:ObjectID>
<a:Name>pk_memory_category</a:Name>
<a:Code>pk_memory_category</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {FAE8231A-44DC-48CF-BE5A-19792B36A2B6}
DAT 1750296238
ORG {8875796A-414A-46AC-BA1E-84E4D7B3BB00}
DAT 1750293620
INS COLNCOL {914664EE-51C0-4DE7-96C0-738BC623DD9A}</a:History>
<c:Key.Columns>
<o:Column Ref="o272"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o288">
<a:ObjectID>503BDB07-032A-4802-B09C-28E2BD966511</a:ObjectID>
<a:Name>ai_prompt_template_PK</a:Name>
<a:Code>ai_prompt_template_PK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o287"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o289">
<a:ObjectID>721C3A25-54F2-426E-863F-C26B3AF38A3A</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o272"/>
</c:Column>
<c:Elements>
<o:Column Ref="o272"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o287"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o48">
<a:ObjectID>9A4AF16A-2226-49E6-B988-F2E86B691B26</a:ObjectID>
<a:Name>AI会话信息表</a:Name>
<a:Code>ai_chat_conversation</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>AI会话信息表</a:Comment>
<a:History>ORG {0F8A38E8-063D-4A77-AC15-9EFB11DCAC18}
DAT **********
ORG {5F944B57-E51C-4B2F-A984-74D4305DD493}
DAT **********</a:History>
<a:Stereotype>ai_chat_conversation</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o290">
<a:ObjectID>DE8FCEE7-92B4-46BB-84D5-3E4EAD679B8A</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {E5146346-3088-4F55-9EF7-CE8A8840B3E7}
DAT **********
ORG {D1EC3673-FAD7-4FA0-A562-5B6DD1B325BC}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o291">
<a:ObjectID>8E1C691E-2D17-4542-8704-68156DC5D53D</a:ObjectID>
<a:Name>应用ID</a:Name>
<a:Code>app_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用ID</a:Comment>
<a:History>ORG {F0BE9451-D20E-4811-85A8-5D4E93E0DA8E}
DAT **********
ORG {0B60C648-AB7B-45B9-A685-9EB6C584ECE1},{82EEBEF1-460A-4B45-A33B-1586C55F2FEE}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o292">
<a:ObjectID>4DDA641D-0F12-4DBD-875E-9305E8D19562</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531392</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>用户ID</a:Comment>
<a:History>ORG {A1661A03-E2A5-408B-9666-3335DC28945D}
DAT **********
ORG {0E99C12F-2F10-41F4-A01E-868BA316C792}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o293">
<a:ObjectID>18BC2461-C040-419E-8517-D25417E400AD</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>title</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>标题</a:Comment>
<a:History>ORG {0F25C393-E322-436D-8411-7342DFFD384D}
DAT **********
ORG {89C7F7E4-3F72-439F-92B8-F3CE7294B0DF}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o294">
<a:ObjectID>B4E3C91E-A365-4567-9EDB-F082C25F9571</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {5F1817E8-A989-406A-B544-D76861BD0865}
DAT **********
ORG {26B15D06-E482-490A-B96A-A5E7C694CB82}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o295">
<a:ObjectID>7B2318F9-4268-4E60-88D9-85F4358A5519</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {CD87C466-2B9E-4907-B45C-81874E259BD1}
DAT **********
ORG {B9201EEF-296C-4C37-B20F-52BC839D0A65}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o296">
<a:ObjectID>98048DD6-8DC0-4557-A42B-234033FF03F8</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {8C55D146-95D5-4305-AF9B-E1C3788C62F3}
DAT **********
ORG {FF848D90-0EDB-4118-9874-F23D25411498}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o297">
<a:ObjectID>2EA24CB5-F097-4AA3-B27A-3AF957830EC9</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {036EF3C1-C8BE-4488-8DE6-161E225FBAEB}
DAT **********
ORG {B6D1EE0A-ADB5-405E-8A55-981F48B87AEB}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o298">
<a:ObjectID>FC2D4156-F83E-47E0-9684-A99233A4EC01</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {8150D9C8-E07B-4552-A637-F46AE9B96CE6}
DAT **********
ORG {2D9E5244-7A7F-4EE5-86E6-CF2C04BA5E52}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o299">
<a:ObjectID>5F87BBF2-8260-4A28-A4FA-19BB02E1C669</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {50AA6D54-A240-42D4-A8D5-3B2EC073AF7E}
DAT **********
ORG {DFA382D2-A980-4A99-BDC0-CF11DDC1702A}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o300">
<a:ObjectID>8568E657-4F5D-422A-B4B4-75408AA954E8</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {4E93C532-422D-42AC-A69E-7D5FC3951E98}
DAT **********
ORG {F51F033B-CB64-48C1-AC23-203A66C5891D}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o301">
<a:ObjectID>E76E20E6-3CDD-42A1-BDAC-3AE99C5471F4</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {FBDDC87A-CCD4-4993-AD88-39A292B87711}
DAT **********
ORG {5EC79D72-509B-4B28-94E6-69EFDB235A88}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o302">
<a:ObjectID>C85A4F9A-54A7-4403-A05E-3F2CB179A258</a:ObjectID>
<a:Name>pk_ai_chat_conversation</a:Name>
<a:Code>pk_ai_chat_conversation</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {6DDF6711-F8AE-45BA-BC79-99BCC59C112E}
DAT **********
ORG {4639B082-0074-4736-A471-E4B8C5067B19}
DAT **********</a:History>
<c:Key.Columns>
<o:Column Ref="o290"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o303">
<a:ObjectID>EF6B5196-A7E0-47E7-B07A-FBEC6208FCE7</a:ObjectID>
<a:Name>ai_chat_conversation_PK</a:Name>
<a:Code>ai_chat_conversation_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o302"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o304">
<a:ObjectID>D1C9AE25-F3D5-4AB8-8C61-94C11B7ABF54</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o290"/>
</c:Column>
<c:Elements>
<o:Column Ref="o290"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o305">
<a:ObjectID>4CB15339-4965-47CB-BE8F-7E10051658C6</a:ObjectID>
<a:Name>Relationship_19_FK</a:Name>
<a:Code>Relationship_19_FK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o12"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o306">
<a:ObjectID>61E56AED-D875-49A6-A8D2-683086E2F667</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o291"/>
</c:Column>
<c:Elements>
<o:Column Ref="o291"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o302"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o49">
<a:ObjectID>D5FAFA19-6128-4B4A-AD46-8D129B937845</a:ObjectID>
<a:Name>AI会话内容表</a:Name>
<a:Code>ai_chat_content</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>AI会话内容表</a:Comment>
<a:History>ORG {46E6748D-1EBF-4AED-8C51-0F961ADFC708}
DAT **********
ORG {EFC7BE76-A235-4097-812F-C4DBBFB4B486}
DAT **********</a:History>
<a:Stereotype>ai_chat_content</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o307">
<a:ObjectID>D144AA52-8196-4A42-B305-C850EAD358F2</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {3B0FFF39-46F0-47E3-9336-0355CA31891A}
DAT **********
ORG {8435A027-6923-4738-8F1D-87A709CF9A2D}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o308">
<a:ObjectID>F954D00F-A87E-4076-A100-072824977AFE</a:ObjectID>
<a:Name>会话ID</a:Name>
<a:Code>conversation_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>会话ID</a:Comment>
<a:History>ORG {83E7F889-D12B-4EFB-907D-AE65E2492D0D}
DAT **********
ORG {D1EC3673-FAD7-4FA0-A562-5B6DD1B325BC},{E6D6F223-1C21-44C9-AA1E-E4B3D1F43798}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o309">
<a:ObjectID>5E9AD09B-F846-4CC7-A395-0567616301FD</a:ObjectID>
<a:Name>应用ID</a:Name>
<a:Code>app_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>应用ID</a:Comment>
<a:History>ORG {FC9B5107-7F7D-47D4-993E-D9CC30C8ECAB}
DAT **********
ORG {0B60C648-AB7B-45B9-A685-9EB6C584ECE1},{E69DBA88-C90F-4ECC-970B-2B46D2E7BDF6}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o310">
<a:ObjectID>97845772-1901-436A-83EB-DD6ED1E36540</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531392</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>用户ID</a:Comment>
<a:History>ORG {582E6EFF-3AAB-41AC-88C4-709897DD5260}
DAT **********
ORG {40DD334B-A1FA-4E9F-9D72-548DEBB975C5}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o311">
<a:ObjectID>3CE5718E-8F9C-404C-9BD0-5448547BF760</a:ObjectID>
<a:Name>模型类型</a:Name>
<a:Code>model_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>模型类型</a:Comment>
<a:History>ORG {5CBF11A9-D0EA-41E8-ADFB-D014B2BC54F4}
DAT **********
ORG {1DE2BCA6-93DC-4900-93D4-02BF10183DFC}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(16)</a:DataType>
<a:Length>16</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o312">
<a:ObjectID>ACBAFC8D-5566-468A-A756-A651B27F6A0E</a:ObjectID>
<a:Name>命令类型</a:Name>
<a:Code>command_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>命令类型</a:Comment>
<a:History>ORG {8DC311F3-F2A2-4560-A449-55A4B3AEAF57}
DAT **********
ORG {3A99E9D4-69A2-4A9B-A6EA-E48F878FD0E8}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o313">
<a:ObjectID>D66BC916-3604-47B7-B6C8-A24173E41796</a:ObjectID>
<a:Name>工具指令</a:Name>
<a:Code>tools_command</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>工具指令</a:Comment>
<a:History>ORG {FA0834D8-ECD2-4571-980B-D85E5E417D18}
DAT **********
ORG {D416B933-468E-45BE-AE63-3A576D9261D3}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o314">
<a:ObjectID>7216BE49-0BEB-4C23-B3F0-239F1608C3E7</a:ObjectID>
<a:Name>用户输入</a:Name>
<a:Code>in_content</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>用户输入</a:Comment>
<a:History>ORG {4FFECACB-8BDB-446F-A09A-1E07E4BE85BE}
DAT **********
ORG {02A88362-3AB5-43E5-8639-FF0280B5E018}
DAT **********
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o315">
<a:ObjectID>0B776B50-27AC-4040-81EE-12DCE559D447</a:ObjectID>
<a:Name>用户输入时间</a:Name>
<a:Code>in_content_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>用户输入时间</a:Comment>
<a:History>ORG {FCE7B183-1622-4D99-AFB9-CADBF3382113}
DAT **********
ORG {95597209-6A12-4BC3-B7D5-5DCF2B04CC6D}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o316">
<a:ObjectID>4E2C3EF5-50EA-48E9-A392-3C40C659E00B</a:ObjectID>
<a:Name>是否命令</a:Name>
<a:Code>is_command</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否命令</a:Comment>
<a:History>ORG {6A067E83-F73F-4C1D-B316-E68E1C58F03B}
DAT **********
ORG {6D56DBA3-60EC-45A9-BCC6-96345D6AE53E}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o317">
<a:ObjectID>19FF6F67-D6DA-43BC-BAD2-74DCDDEA2259</a:ObjectID>
<a:Name>命令的具体内容</a:Name>
<a:Code>command_content</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>命令的具体内容</a:Comment>
<a:History>ORG {30BA9807-290E-4904-B12A-D7402553D2C9}
DAT **********
ORG {A7146677-6D59-430B-8CF2-F677867781CA}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o318">
<a:ObjectID>DCF747BF-DE4C-45BD-B8E5-136F38ADFB24</a:ObjectID>
<a:Name>用户的确认状态</a:Name>
<a:Code>user_confirmation</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>用户的确认状态</a:Comment>
<a:History>ORG {684D04F1-E9A2-4076-9B2E-281A6E2D4157}
DAT **********
ORG {ED747F50-9ADA-4CC3-A37F-247BA1C3DE2E}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o319">
<a:ObjectID>32834E30-2397-46C7-99CC-301B6981093C</a:ObjectID>
<a:Name>确认命令的时间</a:Name>
<a:Code>confirmation_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>确认命令的时间</a:Comment>
<a:History>ORG {C152F0D3-3F92-4D80-91B3-8C0D25FE569D}
DAT **********
ORG {78CF6B8E-653B-44DF-852E-85D0CD8C2B06}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o320">
<a:ObjectID>D31A4155-965D-4C08-890D-349783E1C16D</a:ObjectID>
<a:Name>模型输出数据</a:Name>
<a:Code>out_content_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>模型输出数据</a:Comment>
<a:History>ORG {7646418A-62FB-4F56-A3AF-7BB92DD70C8F}
DAT **********
ORG {9E5975E0-485A-4C26-8708-09BDD9CB48DB}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o321">
<a:ObjectID>A9AF1B1E-9026-493B-A96D-FDD7CCF0775F</a:ObjectID>
<a:Name>模型输出</a:Name>
<a:Code>out_content</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>模型输出</a:Comment>
<a:History>ORG {037A2F58-40AB-4355-87D1-6369B42989A6}
DAT **********
ORG {6A145FAD-58AF-4CB7-8EEB-2347B72E99AE}
DAT **********
ATT LABL</a:History>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o322">
<a:ObjectID>6AB503E3-F205-4ED6-9233-F1173BBDFEE3</a:ObjectID>
<a:Name>输出文本类型0 普通文本|1富文本</a:Name>
<a:Code>out_content_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>输出文本类型0 普通文本|1富文本</a:Comment>
<a:History>ORG {81C10078-7CF8-48FB-AC21-7FB9F7C9178C}
DAT **********
ORG {21E02BCA-82E6-4EE6-A562-0DD5C08CBCC2}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o323">
<a:ObjectID>F0D06AD3-47F0-4FD2-B267-9D5C18DFE3FD</a:ObjectID>
<a:Name>大模型总输出字数</a:Name>
<a:Code>model_text_size</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>大模型总输出字数</a:Comment>
<a:History>ORG {B85640FB-DB34-4B76-89BC-AC957BDF3049}
DAT **********
ORG {77F7BF82-9E78-45D7-8727-EB1F704D8D10}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o324">
<a:ObjectID>C7B3BE3B-8788-40AC-BB6F-6DCA48A4D6B1</a:ObjectID>
<a:Name>大模型输入token数</a:Name>
<a:Code>model_input_tokens</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>大模型输入token数</a:Comment>
<a:History>ORG {8A55575C-DC4E-4507-8AA1-125EBA7D64C3}
DAT **********
ORG {6083B045-3D65-4CD3-84A9-B7C16B479A30}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o325">
<a:ObjectID>359DCB7E-DE45-4BF5-9441-BE3F7C97702B</a:ObjectID>
<a:Name>大模型输出token数</a:Name>
<a:Code>model_output_tokens</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>大模型输出token数</a:Comment>
<a:History>ORG {666CAB39-23DF-47E2-B52A-3AE046B683B5}
DAT **********
ORG {65A633EA-3B42-4080-805F-AC71129E4D78}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o326">
<a:ObjectID>CB36E655-EAE3-45BB-B56D-F2E3323C7F74</a:ObjectID>
<a:Name>提示词</a:Name>
<a:Code>prompt</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531392</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>提示词</a:Comment>
<a:History>ORG {D7D4B73D-C01D-415B-BF61-01E1C00AB51E}
DAT **********
ORG {9659A750-1B1C-4D69-AAD7-CFF64EF8AE88}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(2048)</a:DataType>
<a:Length>2048</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o327">
<a:ObjectID>470764BE-A0D8-4D73-AEFB-3E95CCBA7938</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {D8C6C0B4-6550-468C-9A3C-00879320A577}
DAT **********
ORG {564E2775-BD13-4C49-870F-94EF795FEBA2}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o328">
<a:ObjectID>07F1E8F5-6D4E-44FD-97F6-3743305C34A0</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {149884D4-7D2F-44CB-9001-A3B2CABD0167}
DAT **********
ORG {D33FB0F6-C2C5-419D-8BA9-DA1C643DC50B}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o329">
<a:ObjectID>CB4F6FD7-B6DB-444E-9D6C-4E89C2F0D201</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {9595FEF5-81FF-4E95-A945-5779FE5993EB}
DAT **********
ORG {D4C84342-D315-4C04-B85E-E2941E41AE77}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o330">
<a:ObjectID>35AFC0A6-76EA-4E1D-8D69-BA04E15C8A8D</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {5E863528-1E5D-4E1C-ADDB-CF8C14BF2E13}
DAT **********
ORG {609242D8-D3A9-4D09-BDF8-F1D015B28FC4}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o331">
<a:ObjectID>42B81AB5-60DB-4566-A64C-DA3A0ABD91A0</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {3B5A8378-6E87-4B38-9DE2-995F360B3E66}
DAT **********
ORG {17CE78AB-2616-4BD8-96D4-9EAB55C2CF7F}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o332">
<a:ObjectID>C8F47F26-2658-4D37-805B-214EB055538B</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {989F1A82-A34D-4C6D-BFD2-33245FFCF1B5}
DAT **********
ORG {CA1E2831-E450-4C13-9B00-272E0B5CA73C}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o333">
<a:ObjectID>2022CFC6-0372-4BEB-97FC-A090A82FFAD9</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {26547B55-67DF-4C5A-AB5E-A7E3CCC9CE71}
DAT **********
ORG {46CBFC8A-A045-4853-82E6-BD1763C54C42}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o334">
<a:ObjectID>8A19917E-43BF-4CBC-828B-4D39DEB4A6D5</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {80FFB5F0-C136-4A37-8A8D-417C4624B6DA}
DAT **********
ORG {0B50691F-48CA-4628-8D47-F0124ADA999D}
DAT 1746528518
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o335">
<a:ObjectID>EBBBBDF1-B5ED-4F03-839C-05BC41F93F76</a:ObjectID>
<a:Name>pk_ai_chat_content</a:Name>
<a:Code>pk_ai_chat_content</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {BE2D4F23-7062-4045-A09C-5AAEC9E89183}
DAT **********
ORG {A9832614-EC73-4705-BB98-DC8D8227B9ED}
DAT **********</a:History>
<c:Key.Columns>
<o:Column Ref="o307"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o336">
<a:ObjectID>A468598E-14B8-4999-94B2-FD28BE7104EE</a:ObjectID>
<a:Name>ai_chat_content_PK</a:Name>
<a:Code>ai_chat_content_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o335"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o337">
<a:ObjectID>38C1E760-0B62-44BC-9839-B4C4CCCE167A</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o307"/>
</c:Column>
<c:Elements>
<o:Column Ref="o307"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o338">
<a:ObjectID>1937B348-8D02-4CC8-8002-BC55C59AB82C</a:ObjectID>
<a:Name>Relationship_21_FK</a:Name>
<a:Code>Relationship_21_FK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o18"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o339">
<a:ObjectID>CB3BF3A4-48A7-4699-BD8B-BA2B0EB7C34C</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o308"/>
</c:Column>
<c:Elements>
<o:Column Ref="o308"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o340">
<a:ObjectID>090230BE-C364-4EE8-AE76-AFD186C2C705</a:ObjectID>
<a:Name>Relationship_23_FK</a:Name>
<a:Code>Relationship_23_FK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o22"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o341">
<a:ObjectID>19E94C01-35F0-4E47-A1E6-2053B3374AC6</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o309"/>
</c:Column>
<c:Elements>
<o:Column Ref="o309"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o335"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o50">
<a:ObjectID>B3D669A2-ED63-4209-B6C9-9738198587B8</a:ObjectID>
<a:Name>AI任务表</a:Name>
<a:Code>ai_task</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>AI任务表</a:Comment>
<a:History>ORG {354EF95F-0E66-492A-B13B-3529EA86DBA7}
DAT **********
ORG {ED2D7E6B-72A5-4F10-A7EE-3AC6B27FB885}
DAT **********
MOV COLNCOL</a:History>
<a:Stereotype>ai_task</a:Stereotype>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o342">
<a:ObjectID>CFE6A542-287F-457B-A223-BF446CC4ABCE</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>主键</a:Comment>
<a:History>ORG {773C3BC8-7849-46E3-9BFD-3FBF3F51BC58}
DAT **********
ORG {649026D9-9247-4656-B6AB-CB89263C8D33}
DAT **********
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o343">
<a:ObjectID>A18F3D98-9326-4B52-9189-BE3772BA64E8</a:ObjectID>
<a:Name>上一级任务</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>上一级任务</a:Comment>
<a:History>ORG {9EB69216-1EAC-48CC-A88A-BA49C7F9F00E}
DAT 1750296238
ORG {649026D9-9247-4656-B6AB-CB89263C8D33},{00AA1246-E92B-4A36-BCD0-5AB2981F1313}
DAT 1750294759
ATT NAME
ATT CODE
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o344">
<a:ObjectID>DE6A4273-FDBE-4056-9883-CF1D712C6F24</a:ObjectID>
<a:Name>会话内容</a:Name>
<a:Code>chat_content_id</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>会话内容</a:Comment>
<a:History>ORG {CA26871F-E060-40F0-956D-FD32800E708B}
DAT 1750296238
ORG {8435A027-6923-4738-8F1D-87A709CF9A2D},{101114E2-865F-47D6-8E65-3C4D2BFE81E8}
DAT 1750294759
ATT NAME
ATT CODE
ATT LABL</a:History>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o345">
<a:ObjectID>BCFFE8AC-6872-41B9-A75F-4CF4493A42D8</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>名称</a:Comment>
<a:History>ORG {FC35B725-6355-4BDD-BD25-40D805BFDF54}
DAT **********
ORG {275F8C3D-1841-4394-8804-1CDD280B30C5}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o346">
<a:ObjectID>0BF3CD58-6833-43DA-8E51-687E6AF7480E</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>description</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>描述</a:Comment>
<a:History>ORG {AF3622C7-2A6A-44F4-8DE7-588EDA2ED6E7}
DAT **********
ORG {BFE0F7A5-CD75-4CB8-B4E7-096649D880B3}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o347">
<a:ObjectID>71576B33-D295-4578-AF5B-B6F9C1DE16B0</a:ObjectID>
<a:Name>任务类型0同步任务|1异步任务，默认0</a:Name>
<a:Code>task_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>任务类型0同步任务|1异步任务，默认0</a:Comment>
<a:History>ORG {6C1A702C-8844-47AB-9ED4-63E870DD741E}
DAT **********
ORG {BC6451C5-309F-4D67-AED8-FECB3EC2B0B1}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o348">
<a:ObjectID>85C206E9-766F-4586-92E5-290006A812AB</a:ObjectID>
<a:Name>任务状态-1待处理|2处理中|3成功|4失败|5已过期，默认0</a:Name>
<a:Code>task_status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>任务状态-1待处理|2处理中|3成功|4失败|5已过期，默认0</a:Comment>
<a:History>ORG {6E753C88-59C9-497F-94AE-3AA77BDC4191}
DAT **********
ORG {7070CEDE-5189-44E5-95B3-2B4334AD143D}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o349">
<a:ObjectID>795CCAA2-7E69-4E42-9D8F-043D909877D4</a:ObjectID>
<a:Name>任务执行方式-0串行|1并发，默认0</a:Name>
<a:Code>action_type</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>任务执行方式-0串行|1并发，默认0</a:Comment>
<a:History>ORG {7C05E8BE-C607-4496-BF48-F026F4A0A542}
DAT **********
ORG {2D95BDF4-7E4F-444B-BF14-B3892EB706A2}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o350">
<a:ObjectID>4F658D72-D58E-4137-87C2-D64EDB258CBC</a:ObjectID>
<a:Name>优先级，数字越大优先级越高【0,，99】默认0</a:Name>
<a:Code>priority</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>优先级，数字越大优先级越高【0,，99】默认0</a:Comment>
<a:History>ORG {8F6CC720-5AA5-4D07-810A-D9D3864106E9}
DAT **********
ORG {B9CAFCCA-91C6-40BA-9C1D-6731B471ED23}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o351">
<a:ObjectID>ECB54AB4-6157-4D25-A887-86A728AAAF43</a:ObjectID>
<a:Name>返回码</a:Name>
<a:Code>result_code</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>返回码</a:Comment>
<a:History>ORG {650AA85A-46BC-4DE6-9ECE-72D5A3ABA463}
DAT **********
ORG {69C2879F-1C27-48C0-95D4-139088D4CBE1}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o352">
<a:ObjectID>4A4C0A2E-2454-4095-9A41-94EA2CC30068</a:ObjectID>
<a:Name>返回消息</a:Name>
<a:Code>result_message</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>返回消息</a:Comment>
<a:History>ORG {6BC12E2F-320A-4E00-8921-814B2860E1AB}
DAT **********
ORG {FE1D7840-666D-495A-A01C-8E2D88EEEA9D}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o353">
<a:ObjectID>0D86A81F-FFF9-4520-B17F-1ACE4E8A9498</a:ObjectID>
<a:Name>过期时间</a:Name>
<a:Code>expire_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>过期时间</a:Comment>
<a:History>ORG {70070F79-E451-4F40-8B56-7BC3E8D188BC}
DAT **********
ORG {39F69651-7660-4762-BC7F-E9C5AC7EBD8B}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o354">
<a:ObjectID>8D4D7E23-D4B0-492D-9982-A24B611FC16E</a:ObjectID>
<a:Name>执行ID</a:Name>
<a:Code>execute_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>执行ID</a:Comment>
<a:History>ORG {407BD263-135D-4F4A-BE05-8AA244D2E562}
DAT **********
ORG {B8937E9F-641E-471D-9FE8-6E02B5B9C9D2}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o355">
<a:ObjectID>12B58F8B-C36D-4C46-8FDC-5BDE262A22A8</a:ObjectID>
<a:Name>执行次数</a:Name>
<a:Code>execute_count</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746531082</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>执行次数</a:Comment>
<a:History>ORG {F6AF056A-CE52-4651-B2A6-776A0407E7A5}
DAT **********
ORG {F4F644E2-8CA3-42A1-9B9E-67DF3BD6F2A3}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o356">
<a:ObjectID>2B38929D-C42E-4325-B368-88C9C106E246</a:ObjectID>
<a:Name>业务参数</a:Name>
<a:Code>business_param</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>业务参数</a:Comment>
<a:History>ORG {10A84091-C222-4307-95D2-76B6AF0757BB}
DAT **********
ORG {647E0A76-5E60-4491-BEEC-01E386D7F500}
DAT **********
ATT LABL</a:History>
<a:DataType>varchar(2048)</a:DataType>
<a:Length>2048</a:Length>
</o:Column>
<o:Column Id="o357">
<a:ObjectID>F8BBEEDA-A30F-46B3-974A-0D0AF5D98F5F</a:ObjectID>
<a:Name>响应参数</a:Name>
<a:Code>response_param</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>响应参数</a:Comment>
<a:History>ORG {A00530C1-A613-4AEF-A9D3-B2AE52C15807}
DAT **********
ORG {98795F3F-85E8-47BF-BF45-EB0ACB02028C}
DAT **********
ATT LABL</a:History>
<a:DataType>varchar(2048)</a:DataType>
<a:Length>2048</a:Length>
</o:Column>
<o:Column Id="o358">
<a:ObjectID>D3C02995-82E0-49EE-B109-B729B3D4E63C</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>ext_info</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:History>ORG {00DCCA50-166D-44F4-AB66-533D56BC0FEC}
DAT **********
ORG {5AC5C855-B85B-4658-A303-8F2B8A677408}
DAT **********
ATT LABL</a:History>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o359">
<a:ObjectID>E4AAF3F4-BEF5-42CA-9EFE-3B6A14E124A0</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>状态</a:Comment>
<a:History>ORG {469308D8-849F-4749-A16C-1C4B097FB437}
DAT **********
ORG {76E93618-EC14-4B77-84FF-BD43A816F6F4}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o360">
<a:ObjectID>25048D82-4497-4B57-9366-5EEDF72773A4</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:History>ORG {7889DE06-7572-4148-B776-A0957EC5A532}
DAT **********
ORG {30826875-56A3-4E63-9CB1-0A01C0914F91}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o361">
<a:ObjectID>F17B8195-9DF0-4E7B-8F90-FAF3861E77DA</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:History>ORG {3A4DB23D-C977-4D79-B1EC-5C5A89D30DAA}
DAT **********
ORG {E27091DF-2BD2-4602-95C4-42AC9458B02C}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o362">
<a:ObjectID>F5F54CBC-899A-4672-8584-BD80EEB86FF9</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:History>ORG {026D58E2-337F-4ADF-ABD3-DC28E2A4E909}
DAT **********
ORG {2A12B372-A29C-428E-877D-1CB2736151A6}
DAT **********
ATT LABL</a:History>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o363">
<a:ObjectID>1BA8A408-6B24-4243-A73C-C40AD1950E17</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:History>ORG {24DFA5B4-6ED2-4369-AD28-FB1CFE4CADFC}
DAT **********
ORG {A85EFF40-74F9-4B4D-AA1B-E753B0410703}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>CURRENT_TIMESTAMP</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o364">
<a:ObjectID>B281040F-DC1C-421E-B690-D4E8E85D1F65</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>是否删除</a:Comment>
<a:History>ORG {38CD30DB-7FD0-4A8F-9F73-47D3804DD4E2}
DAT **********
ORG {4B5BD282-DDF8-4D04-BB5B-3D7C9AF5B130}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o365">
<a:ObjectID>CB8D6C6F-0B7E-48FD-AF02-4A7561C06429</a:ObjectID>
<a:Name>备注</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>备注</a:Comment>
<a:History>ORG {B877148A-8C26-4C5C-857E-6BEDB5800287}
DAT **********
ORG {7B0D9ABC-154E-4C62-8877-9704C2876D3C}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o366">
<a:ObjectID>F5D9BDF1-D5E4-4648-8136-A7208A1E6B69</a:ObjectID>
<a:Name>排序</a:Name>
<a:Code>sort</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>排序</a:Comment>
<a:History>ORG {ADA92786-9745-4EF2-BD95-51106171C4E4}
DAT **********
ORG {85F18641-A25D-4A48-82C4-9679441ABE1C}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o367">
<a:ObjectID>85050DC8-1AEB-474D-B11F-7A753DB29494</a:ObjectID>
<a:Name>租户</a:Name>
<a:Code>tenant_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>租户</a:Comment>
<a:History>ORG {88E701C6-D1A3-4199-B9EF-7CE6C162026E}
DAT **********
ORG {872BCA4F-5A66-4046-BE91-66C13C2771FD}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o368">
<a:ObjectID>612A28CD-D1AA-46AC-9DF6-6035E6915632</a:ObjectID>
<a:Name>版本</a:Name>
<a:Code>version</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746530170</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>版本</a:Comment>
<a:History>ORG {1208998C-8EAC-410E-96F6-08A4A07B1D66}
DAT **********
ORG {B06F0251-241A-4299-B134-75C993A652E1}
DAT **********
ATT LABL</a:History>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o369">
<a:ObjectID>CADF97B2-9786-40B9-BDC3-FD8DEE0515EF</a:ObjectID>
<a:Name>意图编码</a:Name>
<a:Code>intent_code</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296606</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Comment>意图编码</a:Comment>
<a:History>ORG {C40334F9-1B15-4961-AE47-238DCD124E4A}
DAT 1750296238
ORG {9A5B477D-F851-487A-8DB5-E427D4706084}
ATT LABL</a:History>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o370">
<a:ObjectID>2AFB21ED-D9C0-4627-8217-EEE66644B898</a:ObjectID>
<a:Name>pk_ai_task</a:Name>
<a:Code>pk_ai_task</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {F537BF46-1FA9-4541-96FC-41F76327134C}
DAT **********
ORG {260AAA9C-385E-42BB-BF4F-90664BEEFB70}
DAT **********</a:History>
<c:Key.Columns>
<o:Column Ref="o342"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o371">
<a:ObjectID>2CA2CE4F-DEA6-4E52-88B8-B16184FA7647</a:ObjectID>
<a:Name>ai_task_PK</a:Name>
<a:Code>ai_task_PK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:Unique>1</a:Unique>
<c:LinkedObject>
<o:Key Ref="o370"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o372">
<a:ObjectID>773F5BCE-0BC6-441D-B7FE-180FBEA85FDC</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o342"/>
</c:Column>
<c:Elements>
<o:Column Ref="o342"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o373">
<a:ObjectID>94D59552-5940-4B5D-BF1D-276FC1F6B83F</a:ObjectID>
<a:Name>Relationship_28_FK</a:Name>
<a:Code>Relationship_28_FK</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:LinkedObject>
<o:Reference Ref="o39"/>
</c:LinkedObject>
<c:IndexColumns>
<o:IndexColumn Id="o374">
<a:ObjectID>85B2CE3C-C5F7-4A78-983D-703A4A11CD28</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<c:Column>
<o:Column Ref="o343"/>
</c:Column>
<c:Elements>
<o:Column Ref="o343"/>
</c:Elements>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o370"/>
</c:PrimaryKey>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o9">
<a:ObjectID>534C53B0-FAA2-4939-8A29-267CC517DF34</a:ObjectID>
<a:Name>app_menu_rel</a:Name>
<a:Code>app_menu_rel</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {DD4C6A12-4076-4778-979A-37FEF6CD52FD}
DAT **********
ORG {4F4F8C47-61A3-4786-91DD-DB5E9EBAE331}
DAT **********</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o44"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o45"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o99"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o375">
<a:ObjectID>F8F5DA22-75A5-4DBB-8535-3975E235B97E</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {AF3DC25B-7D75-4F73-88A2-437DC05A91CB}
DAT **********</a:History>
<c:Object1>
<o:Column Ref="o76"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o103"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o41">
<a:ObjectID>B862750F-297A-4A59-A327-C307EB23B8DE</a:ObjectID>
<a:Name>Relationship_29</a:Name>
<a:Code>Relationship_29</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {5DA1FE67-93A6-4035-9448-EFC78EE1E5D1}
DAT **********
ORG {D6C42762-00EC-4B64-80F4-1593CAEEB024}
DAT 1746529120
ATT PTBL
ATT CTBL
ATT PKEY</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o49"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o50"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o335"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o376">
<a:ObjectID>8F72B0E7-9934-40A1-A3D3-0D840442387A</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {3037C63E-A580-493B-A835-BA6C6E4A4C86}
DAT 1750296238</a:History>
<c:Object1>
<o:Column Ref="o307"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o344"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o29">
<a:ObjectID>ED826D96-E089-4C06-BFB2-483E0C371484</a:ObjectID>
<a:Name>Relationship_12</a:Name>
<a:Code>Relationship_12</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {15B63972-8267-44D7-9A35-761856795F8A}
DAT 1750296238
ORG {23FF23B9-3D24-4C24-A678-56500409EDD1}
DAT 1750293620</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o51"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o52"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o248"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o377">
<a:ObjectID>0A4031C3-4255-4805-9C8E-FE054D398F02</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {F1159352-2253-4FB2-ABBE-6EF1A00F7625}
DAT 1750296238</a:History>
<c:Object1>
<o:Column Ref="o232"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o216"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o36">
<a:ObjectID>E9EAA11C-75F4-4E5D-A5DE-B25FBE0B6DE7</a:ObjectID>
<a:Name>Relationship_14</a:Name>
<a:Code>Relationship_14</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {F80F8308-07CC-4A9C-9299-E72D4333F78E}
DAT 1750296238
ORG {36C6BFAE-7F99-40F8-90F0-3207CF7C236B}
DAT 1750293620</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o56"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o57"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o287"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o378">
<a:ObjectID>3B75F5D2-413F-4DEB-948B-E5EB35460AB7</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {8EBAB451-63BF-4594-9835-5138346B9E75}
DAT 1750296238</a:History>
<c:Object1>
<o:Column Ref="o272"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o252"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o32">
<a:ObjectID>7288F566-C461-4DCE-BBC9-DAF3E63A5AC3</a:ObjectID>
<a:Name>Relationship_13</a:Name>
<a:Code>Relationship_13</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {02523F99-DEDF-4B31-89BC-002C45DCB43E}
DAT 1750296238
ORG {A8B45CA0-319E-4B90-87E7-70C4E0DAA9E1}
DAT 1750293620</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o51"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o55"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o248"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o379">
<a:ObjectID>14709DB8-A18A-49C5-ABBE-228AF05A1918</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {316E6A91-171C-4FB2-8DED-1C43A427E961}
DAT 1750296238</a:History>
<c:Object1>
<o:Column Ref="o232"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o155"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o12">
<a:ObjectID>119784FE-BF70-4A81-A8BF-82151BDC44EB</a:ObjectID>
<a:Name>Relationship_19</a:Name>
<a:Code>Relationship_19</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {B03B7F39-B97D-41E9-81AE-6FDFFE580DC9}
DAT **********
ORG {82EEBEF1-460A-4B45-A33B-1586C55F2FEE}
DAT **********</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o44"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o48"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o99"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o380">
<a:ObjectID>46E8EA67-518B-4E9C-9A43-03DFB6CD3E1C</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {9DC5F6CA-2A44-46D9-983F-F86C381EA23A}
DAT **********</a:History>
<c:Object1>
<o:Column Ref="o76"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o291"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o15">
<a:ObjectID>222FA9E8-D1D6-42EF-A59B-6008C7E32AAB</a:ObjectID>
<a:Name>Relationship_20</a:Name>
<a:Code>Relationship_20</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {0C20D6CF-A8CF-4A7D-85A2-162250A78636}
DAT **********
ORG {34193417-1724-49E0-9CF7-8A4676E6C7FC}
DAT **********</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o48"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o47"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o302"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o381">
<a:ObjectID>04C567EB-B7E6-4D43-9375-D616C4A93DA2</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {ED4FF14D-9C6B-4E42-BF3F-C37E7C78C5E9}
DAT **********</a:History>
<c:Object1>
<o:Column Ref="o290"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o193"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o18">
<a:ObjectID>70AD2904-4799-439B-BA53-D63D144F9865</a:ObjectID>
<a:Name>Relationship_21</a:Name>
<a:Code>Relationship_21</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {B6C7C43C-DBD0-49DE-BFEE-6B65C15A681B}
DAT **********
ORG {E6D6F223-1C21-44C9-AA1E-E4B3D1F43798}
DAT **********</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o48"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o49"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o302"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o382">
<a:ObjectID>D267EFC0-FA43-454B-9DA9-267FD69C005E</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {94C72169-9678-4051-BC98-8C4100CBD877}
DAT **********</a:History>
<c:Object1>
<o:Column Ref="o290"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o308"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o20">
<a:ObjectID>1ECA0E0C-A14E-4260-A9C6-C54003CDA9A2</a:ObjectID>
<a:Name>Relationship_22</a:Name>
<a:Code>Relationship_22</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {E1234E7E-3779-41A7-AADB-3B1495D922C5}
DAT **********
ORG {C657EE89-1075-41F6-AFFF-6FDF801262B5}
DAT **********</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o49"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o47"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o335"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o383">
<a:ObjectID>5B3A6ADB-7224-470A-869C-B6AF56DE9203</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {9F564451-4FDE-40D0-AE26-829E6E3960EB}
DAT **********</a:History>
<c:Object1>
<o:Column Ref="o307"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o194"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o22">
<a:ObjectID>EF78FE77-7AD8-4EC5-9A4C-4A4EC844FECB</a:ObjectID>
<a:Name>Relationship_23</a:Name>
<a:Code>Relationship_23</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {83840849-FE88-4755-85AA-459E70317BAA}
DAT **********
ORG {E69DBA88-C90F-4ECC-970B-2B46D2E7BDF6}
DAT **********</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o44"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o49"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o99"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o384">
<a:ObjectID>16906BD4-AF64-4EF9-B7D8-59668ED9920D</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {CEEE6596-CDCA-484C-A82D-0652F7CC90D8}
DAT **********</a:History>
<c:Object1>
<o:Column Ref="o76"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o309"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o25">
<a:ObjectID>F5E87788-D763-4434-A12C-A38B2C0D679D</a:ObjectID>
<a:Name>Relationship_24</a:Name>
<a:Code>Relationship_24</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {A1A99999-F268-4837-8E0E-6CCD6035A89E}
DAT **********
ORG {DCCABB93-E754-4465-BAD5-492DE81075E9}
DAT **********</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o48"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o46"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o302"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o385">
<a:ObjectID>D5770A49-3088-412B-8424-28B49B85DFBC</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {D7867EDA-BAFA-4062-B902-0974D494B335}
DAT **********</a:History>
<c:Object1>
<o:Column Ref="o290"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o135"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o39">
<a:ObjectID>C6E0204E-F428-401A-A8FB-EE7D6E5ED15E</a:ObjectID>
<a:Name>Relationship_28</a:Name>
<a:Code>Relationship_28</a:Code>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {443E26FF-A5F9-4F05-973A-DE3237BC5EA1}
DAT 1750296238
ORG {00AA1246-E92B-4A36-BCD0-5AB2981F1313}
DAT 1750294759</a:History>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o50"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o50"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o370"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o386">
<a:ObjectID>4F135FF9-A5C9-406D-B38D-EFFD01A68CCC</a:ObjectID>
<a:CreationDate>1750296238</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296243</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:History>ORG {2058DA2B-A9D9-4D9B-8B6F-E8DB117A7AE3}
DAT 1750296238</a:History>
<c:Object1>
<o:Column Ref="o342"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o343"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
<c:DefaultGroups>
<o:Group Id="o387">
<a:ObjectID>5CBC2AFA-0157-4A96-B5B0-C17A23B8637F</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1746529262</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1746529262</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o388">
<a:ObjectID>0A8B897A-87A3-4D3F-A694-D68C9E536B3F</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1746529264</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1242731549</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:TargetModelURL>file:///%[XDB]%/mysql50.xdb</a:TargetModelURL>
<a:TargetModelID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<a:TargetModelLastModificationDate>1242731549</a:TargetModelLastModificationDate>
<c:SessionShortcuts>
<o:Shortcut Ref="o4"/>
</c:SessionShortcuts>
</o:TargetModel>
<o:TargetModel Id="o389">
<a:ObjectID>ABCE0566-F387-40FF-99D1-3168D6005011</a:ObjectID>
<a:Name>aiAgent</a:Name>
<a:Code>aiAgent</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>johnz</a:Creator>
<a:ModificationDate>1750296217</a:ModificationDate>
<a:Modifier>johnz</a:Modifier>
<a:TargetModelURL>file:///D|/wks/tech_middle_platform/aiagent/design/aiAgent.ldm</a:TargetModelURL>
<a:TargetModelID>********-2EB4-4CDC-B4E1-6489961808EC</a:TargetModelID>
<a:TargetModelClassID>5F45F978-C4F3-4E35-A3FC-AF3318663A0F</a:TargetModelClassID>
<a:TargetModelLastModificationDate>1750296217</a:TargetModelLastModificationDate>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>