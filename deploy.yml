---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: rc-platform-testagent
  labels:
    k8s.kuboard.cn/layer: svc
    k8s.kuboard.cn/name: rc-platform-testagent
  annotations:
    deployment.kubernetes.io/revision: '1'
    k8s.kuboard.cn/workload: rc-platform-testagent
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s.kuboard.cn/layer: svc
      k8s.kuboard.cn/name: rc-platform-testagent
  template:
    metadata:
      creationTimestamp: null
      labels:
        k8s.kuboard.cn/layer: svc
        k8s.kuboard.cn/name: rc-platform-testagent
    spec:
      nodeSelector:
        accelerator: testagent
      containers:
        - name: rc-platform-testagent
          image: 'hb.richinfo.devops/richinfo-platform/rc-platform-testagent:1.0.1'
          args:
            - '--spring.profiles.active=${PROFILE}'
          ports:
            - name: server
              containerPort: 9302
              protocol: TCP
          envFrom:
            - configMapRef:
                name: richinfo-config
          resources:
            limits:
              memory: 2Gi
            requests:
              memory: 200Mi
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 9302
              scheme: HTTP
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 9302
              scheme: HTTP
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 9302
              scheme: HTTP
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 20
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600

---
apiVersion: v1
kind: Service
metadata:
  name: rc-platform-testagent
  labels:
    k8s.kuboard.cn/layer: svc
    k8s.kuboard.cn/name: rc-platform-testagent
spec:
  type: NodePort
  selector:
    k8s.kuboard.cn/layer: svc
    k8s.kuboard.cn/name: rc-platform-testagent
  nodeSelector:
    accelerator: testagent
  ports:
    - protocol: TCP
      port: 9302
      targetPort: 9302
      nodePort: 19302