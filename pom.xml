<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>richinfo-platform-service</artifactId>
        <groupId>org.richinfo.framework</groupId>
        <version>1.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>rc-platform-testagent</artifactId>
    <name>${project.artifactId}</name>
    <version>${richinfo.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.richinfo.framework</groupId>
            <artifactId>richinfo-core-boot</artifactId>
            <version>${richinfo.tool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.richinfo.framework</groupId>
            <artifactId>richinfo-platform-system-api</artifactId>
            <version>${richinfo.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.3</version>
        </dependency>

        <!-- OkHttp for HTTP client -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>

        <!-- OkHttp SSE for Server-Sent Events -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
            <version>4.12.0</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
