/**
 * richinfo
 */


package org.rc.platform.testagent.memory.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.memory.entity.MemoryAccessLog;
import org.rc.platform.testagent.memory.vo.MemoryAccessLogVO;


/**
 * 记忆访问记录表(MemoryAccessLog)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:51
 */
public class MemoryAccessLogWrapper extends BaseEntityWrapper<MemoryAccessLog, MemoryAccessLogVO> {

    public static MemoryAccessLogWrapper build() {
        return new MemoryAccessLogWrapper();
    }

    @Override
    public MemoryAccessLogVO entityVO(MemoryAccessLog memoryAccessLog) {
        MemoryAccessLogVO memoryAccessLogVO = BeanUtil.copyProperties(memoryAccessLog, MemoryAccessLogVO.class);

        return memoryAccessLogVO;
    }

}

