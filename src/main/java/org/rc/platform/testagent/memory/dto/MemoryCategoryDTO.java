package org.rc.platform.testagent.memory.dto;


import org.rc.platform.testagent.memory.entity.MemoryCategory;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 记忆分类(MemoryCategory)数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemoryCategoryDTO extends MemoryCategory {
    private static final long serialVersionUID = 1L;

}

