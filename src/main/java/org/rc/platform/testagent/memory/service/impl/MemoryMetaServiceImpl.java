package org.rc.platform.testagent.memory.service.impl;


import org.rc.platform.testagent.memory.entity.MemoryMeta;
import org.rc.platform.testagent.memory.mapper.MemoryMetaMapper;
import org.rc.platform.testagent.memory.vo.MemoryMetaVO;
import org.rc.platform.testagent.memory.service.MemoryMetaService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆体元数据(MemoryMeta)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:53
 */
@Service
public class MemoryMetaServiceImpl extends BaseServiceImpl<MemoryMetaMapper, MemoryMeta> implements MemoryMetaService {

    @Override
    public IPage<MemoryMetaVO> selectMemoryMetaPage(IPage<MemoryMetaVO> page, MemoryMetaVO memoryMeta) {
        return page.setRecords(baseMapper.selectMemoryMetaPage(page, memoryMeta));
    }

}
