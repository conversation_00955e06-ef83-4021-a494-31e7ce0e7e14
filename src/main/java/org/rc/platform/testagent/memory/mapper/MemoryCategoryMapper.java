package org.rc.platform.testagent.memory.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.memory.entity.MemoryCategory;

import org.rc.platform.testagent.memory.vo.MemoryCategoryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆分类(MemoryCategory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:52
 */
public interface MemoryCategoryMapper extends BaseMapper<MemoryCategory> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<MemoryCategory> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<MemoryCategory> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<MemoryCategory> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<MemoryCategory> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param memoryCategory
     * @return
     */
    List<MemoryCategoryVO> selectMemoryCategoryPage(IPage page, MemoryCategoryVO memoryCategory);

}
