package org.rc.platform.testagent.memory.vo;


import org.rc.platform.testagent.memory.entity.MemoryMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 记忆体元数据(MemoryMeta)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "记忆体元数据")
public class MemoryMetaVO extends MemoryMeta {
    private static final long serialVersionUID = 1L;

}

