package org.rc.platform.testagent.memory.vo;


import org.rc.platform.testagent.memory.entity.MemoryAccessLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 记忆访问记录表(MemoryAccessLog)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "记忆访问记录表")
public class MemoryAccessLogVO extends MemoryAccessLog {
    private static final long serialVersionUID = 1L;

}

