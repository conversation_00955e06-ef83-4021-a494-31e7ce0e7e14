/**
 * richinfo
 */


package org.rc.platform.testagent.memory.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.memory.entity.MemoryMeta;
import org.rc.platform.testagent.memory.vo.MemoryMetaVO;


/**
 * 记忆体元数据(MemoryMeta)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:53
 */
public class MemoryMetaWrapper extends BaseEntityWrapper<MemoryMeta, MemoryMetaVO> {

    public static MemoryMetaWrapper build() {
        return new MemoryMetaWrapper();
    }

    @Override
    public MemoryMetaVO entityVO(MemoryMeta memoryMeta) {
        MemoryMetaVO memoryMetaVO = BeanUtil.copyProperties(memoryMeta, MemoryMetaVO.class);

        return memoryMetaVO;
    }

}

