/**
 * richinfo
 */


package org.rc.platform.testagent.memory.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.memory.entity.Memory;
import org.rc.platform.testagent.memory.vo.MemoryVO;


/**
 * 记忆实体(Memory)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:48
 */
public class MemoryWrapper extends BaseEntityWrapper<Memory, MemoryVO> {

    public static MemoryWrapper build() {
        return new MemoryWrapper();
    }

    @Override
    public MemoryVO entityVO(Memory memory) {
        MemoryVO memoryVO = BeanUtil.copyProperties(memory, MemoryVO.class);

        return memoryVO;
    }

}

