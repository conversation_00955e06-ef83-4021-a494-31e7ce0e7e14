package org.rc.platform.testagent.memory.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.rc.platform.testagent.memory.entity.Memory;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SaveMemoryDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private String userId;

    /**
     * 记忆列表
     */
    @NotEmpty(message = "记忆列表不能为空")
    private List<Memory> memoryList;
}
