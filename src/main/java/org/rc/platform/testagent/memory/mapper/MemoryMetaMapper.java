package org.rc.platform.testagent.memory.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.memory.entity.MemoryMeta;

import org.rc.platform.testagent.memory.vo.MemoryMetaVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆体元数据(MemoryMeta)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:53
 */
public interface MemoryMetaMapper extends BaseMapper<MemoryMeta> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<MemoryMeta> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<MemoryMeta> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<MemoryMeta> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<MemoryMeta> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param memoryMeta
     * @return
     */
    List<MemoryMetaVO> selectMemoryMetaPage(IPage page, MemoryMetaVO memoryMeta);

}
