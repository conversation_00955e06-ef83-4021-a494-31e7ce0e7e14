package org.rc.platform.testagent.memory.entity;

import java.util.Date;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * 记忆实体(Memory)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:48
 */
@Data
@TableName(value = "ai_agent_memory")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class Memory extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 重写父类字段，标记为数据库中不存在
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private Long conversationId;
    /**
     * 角色
     */
    @Schema(description = "角色")
    private String role;
    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;
    /**
     * 重要性评分
     */
    @Schema(description = "重要性评分")
    private Object importanceScore;
    /**
     * 相关性评分
     */
    @Schema(description = "相关性评分")
    private Object relevanceScore;
    /**
     * 是否关键
     */
    @Schema(description = "是否关键")
    private Integer isCritical;
    /**
     * 记忆类型-0会话|1知识库|2事件
     */
    @Schema(description = "记忆类型-0会话|1知识库|2事件")
    private Integer memoryType;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

}
