package org.rc.platform.testagent.memory.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.memory.entity.Memory;

import org.rc.platform.testagent.memory.vo.MemoryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆实体(Memory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:48
 */
public interface MemoryMapper extends BaseMapper<Memory> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<Memory> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<Memory> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<Memory> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<Memory> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param memory
     * @return
     */
    List<MemoryVO> selectMemoryPage(IPage page, MemoryVO memory);

}
