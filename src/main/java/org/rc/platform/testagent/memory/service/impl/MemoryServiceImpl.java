package org.rc.platform.testagent.memory.service.impl;


import org.rc.platform.testagent.memory.dto.SaveMemoryDTO;
import org.rc.platform.testagent.memory.entity.Memory;
import org.rc.platform.testagent.memory.mapper.MemoryMapper;
import org.rc.platform.testagent.memory.vo.MemoryVO;
import org.rc.platform.testagent.memory.service.MemoryService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆实体(Memory)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:48
 */
@Service
public class MemoryServiceImpl extends BaseServiceImpl<MemoryMapper, Memory> implements MemoryService {

    @Override
    public IPage<MemoryVO> selectMemoryPage(IPage<MemoryVO> page, MemoryVO memory) {
        return page.setRecords(baseMapper.selectMemoryPage(page, memory));
    }

    @Override
    public Boolean save(SaveMemoryDTO saveMemoryDTO) {
        baseMapper.insertBatch(saveMemoryDTO.getMemoryList());
        return Boolean.TRUE;
    }

}
