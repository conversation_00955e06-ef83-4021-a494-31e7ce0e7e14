/**
 * richinfo
 */


package org.rc.platform.testagent.memory.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.memory.entity.MemoryCategory;
import org.rc.platform.testagent.memory.vo.MemoryCategoryVO;


/**
 * 记忆分类(MemoryCategory)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:52
 */
public class MemoryCategoryWrapper extends BaseEntityWrapper<MemoryCategory, MemoryCategoryVO> {

    public static MemoryCategoryWrapper build() {
        return new MemoryCategoryWrapper();
    }

    @Override
    public MemoryCategoryVO entityVO(MemoryCategory memoryCategory) {
        MemoryCategoryVO memoryCategoryVO = BeanUtil.copyProperties(memoryCategory, MemoryCategoryVO.class);

        return memoryCategoryVO;
    }

}

