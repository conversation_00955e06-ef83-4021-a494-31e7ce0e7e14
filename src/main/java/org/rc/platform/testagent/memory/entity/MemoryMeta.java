package org.rc.platform.testagent.memory.entity;

import java.util.Date;


import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * 记忆体元数据(MemoryMeta)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:53
 */
@Data
@TableName(value = "ai_memory_meta")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class MemoryMeta extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 记忆实体
     */
    @Schema(description = "记忆实体")
    private Long agentMemoryId;
    /**
     * 记忆key
     */
    @Schema(description = "记忆key")
    private String memoryKey;
    /**
     * 记忆value
     */
    @Schema(description = "记忆value")
    private String memoryValue;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

}
