package org.rc.platform.testagent.memory.service;


import org.rc.platform.testagent.memory.entity.MemoryAccessLog;
import org.rc.platform.testagent.memory.vo.MemoryAccessLogVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆访问记录表(MemoryAccessLog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:51
 */
public interface MemoryAccessLogService extends BaseService<MemoryAccessLog> {

    /**
     * 自定义分页
     *
     * @param page
     * @param memoryAccessLog
     * @return
     */
    IPage<MemoryAccessLogVO> selectMemoryAccessLogPage(IPage<MemoryAccessLogVO> page, MemoryAccessLogVO memoryAccessLog);
}
