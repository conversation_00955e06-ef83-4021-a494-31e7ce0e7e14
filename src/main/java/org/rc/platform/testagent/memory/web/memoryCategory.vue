<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.memoryCategory_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove} from "@/api/demo/memoryCategoryApi";
import {mapGetters} from "vuex";

export default {
  data() {
    return {
      form: {},
      query: {},
      search: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 210,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        column: [
          {
            label: "主键",
            prop: "id",
            rules: [{
              required: true,
              message: "请输入主键",
              trigger: "blur"
            }]
          },
          {
            label: "分类名称",
            prop: "categoryName",
            rules: [{
              required: true,
              message: "请输入分类名称",
              trigger: "blur"
            }]
          },
          {
            label: "上一级",
            prop: "parentId",
            rules: [{
              required: true,
              message: "请输入上一级",
              trigger: "blur"
            }]
          },
          {
            label: "描述",
            prop: "description",
            rules: [{
              required: true,
              message: "请输入描述",
              trigger: "blur"
            }]
          },
          {
            label: "创建时间",
            prop: "createTime",
            rules: [{
              required: true,
              message: "请输入创建时间",
              trigger: "blur"
            }]
          },
          {
            label: "更新时间",
            prop: "updateTime",
            rules: [{
              required: true,
              message: "请输入更新时间",
              trigger: "blur"
            }]
          },
          {
            label: "租户",
            prop: "tenantId",
            rules: [{
              required: true,
              message: "请输入租户",
              trigger: "blur"
            }]
          },
          {
            label: "版本",
            prop: "version",
            rules: [{
              required: true,
              message: "请输入版本",
              trigger: "blur"
            }]
          },
          {
            label: "创建人",
            prop: "createUser",
            rules: [{
              required: true,
              message: "请输入创建人",
              trigger: "blur"
            }]
          },
          {
            label: "更新人",
            prop: "updateUser",
            rules: [{
              required: true,
              message: "请输入更新人",
              trigger: "blur"
            }]
          },
          {
            label: "是否删除",
            prop: "isDeleted",
            rules: [{
              required: true,
              message: "请输入是否删除",
              trigger: "blur"
            }]
          },
          {
            label: "状态",
            prop: "status",
            rules: [{
              required: true,
              message: "请输入状态",
              trigger: "blur"
            }]
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.memoryCategory_add, false),
        viewBtn: this.validData(this.permission.memoryCategory_view, false),
        delBtn: this.validData(this.permission.memoryCategory_delete, false),
        editBtn: this.validData(this.permission.memoryCategory_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    rowSave(row, done, loading) {
      add(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        done();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
