package org.rc.platform.testagent.memory.service;


import org.rc.platform.testagent.memory.dto.SaveMemoryDTO;
import org.rc.platform.testagent.memory.entity.Memory;
import org.rc.platform.testagent.memory.vo.MemoryVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆实体(Memory)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:48
 */
public interface MemoryService extends BaseService<Memory> {

    /**
     * 自定义分页
     *
     * @param page
     * @param memory
     * @return
     */
    IPage<MemoryVO> selectMemoryPage(IPage<MemoryVO> page, MemoryVO memory);

    /**
     * 保存
     * @param saveMemoryDTO
     * @return
     */
    Boolean save(SaveMemoryDTO saveMemoryDTO);
}
