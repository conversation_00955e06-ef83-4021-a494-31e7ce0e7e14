package org.rc.platform.testagent.memory.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.rc.platform.testagent.memory.dto.SaveMemoryDTO;
import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.memory.entity.Memory;

import org.rc.platform.testagent.memory.vo.MemoryVO;
import org.rc.platform.testagent.memory.wrapper.MemoryWrapper;


import org.rc.platform.testagent.memory.service.MemoryService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 记忆实体(Memory)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:48
 */
@RestController
@RequestMapping("/memory")
@Tag(name = "Memory", description = "记忆实体接口")
public class MemoryController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private MemoryService memoryService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入memory")
    public R<MemoryVO> detail(Memory memory) {
        Memory detail = memoryService.getOne(Condition.getQueryWrapper(memory));
        return R.data(MemoryWrapper.build().entityVO(detail));
    }

    /**
     * 分页 memory
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入memory")
    public R<IPage<MemoryVO>> list(Memory memory, Query query) {
        IPage<Memory> pages = memoryService.page(Condition.getPage(query), Condition.getQueryWrapper(memory));
        return R.data(MemoryWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 memory
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入memory")
    public R<IPage<MemoryVO>> page(MemoryVO memory, Query query) {
        IPage<MemoryVO> pages = memoryService.selectMemoryPage(Condition.getPage(query), memory);
        return R.data(pages);
    }

    /**
     * 新增 memory
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入memory")
    public R save(@Valid @RequestBody SaveMemoryDTO saveMemoryDTO) {
        return R.status(memoryService.save(saveMemoryDTO));
    }

    /**
     * 修改 memory
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入memory")
    public R update(@Valid @RequestBody Memory memory) {
        return R.status(memoryService.updateById(memory));
    }

    /**
     * 新增或修改 memory
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入memory")
    public R submit(@Valid @RequestBody Memory memory) {
        return R.status(memoryService.saveOrUpdate(memory));
    }


    /**
     * 删除 memory
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(memoryService.deleteLogic(Func.toLongList(ids)));
    }


}
