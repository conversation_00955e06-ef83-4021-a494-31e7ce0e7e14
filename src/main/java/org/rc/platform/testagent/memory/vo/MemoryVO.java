package org.rc.platform.testagent.memory.vo;


import org.rc.platform.testagent.memory.entity.Memory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 记忆实体(Memory)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "记忆实体")
public class MemoryVO extends Memory {
    private static final long serialVersionUID = 1L;

}

