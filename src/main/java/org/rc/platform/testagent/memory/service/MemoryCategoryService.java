package org.rc.platform.testagent.memory.service;


import org.rc.platform.testagent.memory.entity.MemoryCategory;
import org.rc.platform.testagent.memory.vo.MemoryCategoryVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆分类(MemoryCategory)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:52
 */
public interface MemoryCategoryService extends BaseService<MemoryCategory> {

    /**
     * 自定义分页
     *
     * @param page
     * @param memoryCategory
     * @return
     */
    IPage<MemoryCategoryVO> selectMemoryCategoryPage(IPage<MemoryCategoryVO> page, MemoryCategoryVO memoryCategory);
}
