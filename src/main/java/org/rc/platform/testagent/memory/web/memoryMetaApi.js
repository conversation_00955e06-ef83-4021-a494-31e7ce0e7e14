import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/demo/memoryMeta/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/demo/memoryMeta/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/demo/memoryMeta/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/demo/memoryMeta/submit',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/demo/memoryMeta/submit',
        method: 'post',
        data: row
    })
}
