package org.rc.platform.testagent.memory.vo;


import org.rc.platform.testagent.memory.entity.MemoryCategory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 记忆分类(MemoryCategory)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "记忆分类")
public class MemoryCategoryVO extends MemoryCategory {
    private static final long serialVersionUID = 1L;

}

