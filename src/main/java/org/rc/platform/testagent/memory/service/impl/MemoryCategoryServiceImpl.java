package org.rc.platform.testagent.memory.service.impl;


import org.rc.platform.testagent.memory.entity.MemoryCategory;
import org.rc.platform.testagent.memory.mapper.MemoryCategoryMapper;
import org.rc.platform.testagent.memory.vo.MemoryCategoryVO;
import org.rc.platform.testagent.memory.service.MemoryCategoryService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆分类(MemoryCategory)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:52
 */
@Service
public class MemoryCategoryServiceImpl extends BaseServiceImpl<MemoryCategoryMapper, MemoryCategory> implements MemoryCategoryService {

    @Override
    public IPage<MemoryCategoryVO> selectMemoryCategoryPage(IPage<MemoryCategoryVO> page, MemoryCategoryVO memoryCategory) {
        return page.setRecords(baseMapper.selectMemoryCategoryPage(page, memoryCategory));
    }

}
