package org.rc.platform.testagent.memory.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.memory.entity.MemoryCategory;

import org.rc.platform.testagent.memory.vo.MemoryCategoryVO;
import org.rc.platform.testagent.memory.wrapper.MemoryCategoryWrapper;


import org.rc.platform.testagent.memory.service.MemoryCategoryService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 记忆分类(MemoryCategory)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:51
 */
@RestController
@RequestMapping("/memoryCategory")
@Tag(name = "MemoryCategory", description = "记忆分类接口")
public class MemoryCategoryController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private MemoryCategoryService memoryCategoryService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入memoryCategory")
    public R<MemoryCategoryVO> detail(MemoryCategory memoryCategory) {
        MemoryCategory detail = memoryCategoryService.getOne(Condition.getQueryWrapper(memoryCategory));
        return R.data(MemoryCategoryWrapper.build().entityVO(detail));
    }

    /**
     * 分页 memoryCategory
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入memoryCategory")
    public R<IPage<MemoryCategoryVO>> list(MemoryCategory memoryCategory, Query query) {
        IPage<MemoryCategory> pages = memoryCategoryService.page(Condition.getPage(query), Condition.getQueryWrapper(memoryCategory));
        return R.data(MemoryCategoryWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 memoryCategory
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入memoryCategory")
    public R<IPage<MemoryCategoryVO>> page(MemoryCategoryVO memoryCategory, Query query) {
        IPage<MemoryCategoryVO> pages = memoryCategoryService.selectMemoryCategoryPage(Condition.getPage(query), memoryCategory);
        return R.data(pages);
    }

    /**
     * 新增 memoryCategory
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入memoryCategory")
    public R save(@Valid @RequestBody MemoryCategory memoryCategory) {
        return R.status(memoryCategoryService.save(memoryCategory));
    }

    /**
     * 修改 memoryCategory
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入memoryCategory")
    public R update(@Valid @RequestBody MemoryCategory memoryCategory) {
        return R.status(memoryCategoryService.updateById(memoryCategory));
    }

    /**
     * 新增或修改 memoryCategory
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入memoryCategory")
    public R submit(@Valid @RequestBody MemoryCategory memoryCategory) {
        return R.status(memoryCategoryService.saveOrUpdate(memoryCategory));
    }


    /**
     * 删除 memoryCategory
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(memoryCategoryService.deleteLogic(Func.toLongList(ids)));
    }


}
