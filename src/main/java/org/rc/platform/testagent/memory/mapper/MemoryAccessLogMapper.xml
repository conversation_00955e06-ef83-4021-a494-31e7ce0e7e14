<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.memory.mapper.MemoryAccessLogMapper">

    <resultMap type="org.rc.platform.testagent.memory.entity.MemoryAccessLog" id="memoryAccessLogResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="agentMemoryId" column="agent_memory_id" jdbcType="INTEGER"/>
        <result property="accessTime" column="access_time" jdbcType="TIMESTAMP"/>
        <result property="accessType" column="access_type" jdbcType="INTEGER"/>
        <result property="accessedBy" column="accessed_by" jdbcType="INTEGER"/>
        <result property="queryContext" column="query_context" jdbcType="VARCHAR"/>
        <result property="metadata" column="metadata" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_memory_access_log(agent_memory_id, access_time, access_type, accessed_by, query_context,
        metadata, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.agentMemoryId}, #{entity.accessTime}, #{entity.accessType}, #{entity.accessedBy},
            #{entity.queryContext}, #{entity.metadata}, #{entity.createTime}, #{entity.updateTime}, #{entity.tenantId},
            #{entity.version}, #{entity.createUser}, #{entity.updateUser}, #{entity.isDeleted}, #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_memory_access_log(agent_memory_id, access_time, access_type, accessed_by, query_context,
        metadata, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.agentMemoryId}, #{entity.accessTime}, #{entity.accessType}, #{entity.accessedBy},
            #{entity.queryContext}, #{entity.metadata}, #{entity.createTime}, #{entity.updateTime}, #{entity.tenantId},
            #{entity.version}, #{entity.createUser}, #{entity.updateUser}, #{entity.isDeleted}, #{entity.status})
        </foreach>
        on duplicate key update
        agent_memory_id = values(agent_memory_id) , access_time = values(access_time) , access_type =
        values(access_type) , accessed_by = values(accessed_by) , query_context = values(query_context) , metadata =
        values(metadata) , create_time = values(create_time) , update_time = values(update_time) , tenant_id =
        values(tenant_id) , version = values(version) , create_user = values(create_user) , update_user =
        values(update_user) , is_deleted = values(is_deleted) , status = values(status)
    </insert>
    <select id="selectMemoryAccessLogPage" resultMap="memoryAccessLogResultMap">
        select *
        from ai_memory_access_log
        where is_deleted = 0
    </select>

</mapper>
