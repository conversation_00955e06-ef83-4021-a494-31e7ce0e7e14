package org.rc.platform.testagent.memory.dto;


import org.rc.platform.testagent.memory.entity.MemoryAccessLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 记忆访问记录表(MemoryAccessLog)数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemoryAccessLogDTO extends MemoryAccessLog {
    private static final long serialVersionUID = 1L;

}

