package org.rc.platform.testagent.memory.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.memory.entity.MemoryAccessLog;

import org.rc.platform.testagent.memory.vo.MemoryAccessLogVO;
import org.rc.platform.testagent.memory.wrapper.MemoryAccessLogWrapper;


import org.rc.platform.testagent.memory.service.MemoryAccessLogService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 记忆访问记录表(MemoryAccessLog)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:50
 */
@RestController
@RequestMapping("/memoryAccessLog")
@Tag(name = "MemoryAccessLog", description = "记忆访问记录表接口")
public class MemoryAccessLogController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private MemoryAccessLogService memoryAccessLogService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入memoryAccessLog")
    public R<MemoryAccessLogVO> detail(MemoryAccessLog memoryAccessLog) {
        MemoryAccessLog detail = memoryAccessLogService.getOne(Condition.getQueryWrapper(memoryAccessLog));
        return R.data(MemoryAccessLogWrapper.build().entityVO(detail));
    }

    /**
     * 分页 memoryAccessLog
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入memoryAccessLog")
    public R<IPage<MemoryAccessLogVO>> list(MemoryAccessLog memoryAccessLog, Query query) {
        IPage<MemoryAccessLog> pages = memoryAccessLogService.page(Condition.getPage(query), Condition.getQueryWrapper(memoryAccessLog));
        return R.data(MemoryAccessLogWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 memoryAccessLog
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入memoryAccessLog")
    public R<IPage<MemoryAccessLogVO>> page(MemoryAccessLogVO memoryAccessLog, Query query) {
        IPage<MemoryAccessLogVO> pages = memoryAccessLogService.selectMemoryAccessLogPage(Condition.getPage(query), memoryAccessLog);
        return R.data(pages);
    }

    /**
     * 新增 memoryAccessLog
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入memoryAccessLog")
    public R save(@Valid @RequestBody MemoryAccessLog memoryAccessLog) {
        return R.status(memoryAccessLogService.save(memoryAccessLog));
    }

    /**
     * 修改 memoryAccessLog
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入memoryAccessLog")
    public R update(@Valid @RequestBody MemoryAccessLog memoryAccessLog) {
        return R.status(memoryAccessLogService.updateById(memoryAccessLog));
    }

    /**
     * 新增或修改 memoryAccessLog
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入memoryAccessLog")
    public R submit(@Valid @RequestBody MemoryAccessLog memoryAccessLog) {
        return R.status(memoryAccessLogService.saveOrUpdate(memoryAccessLog));
    }


    /**
     * 删除 memoryAccessLog
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(memoryAccessLogService.deleteLogic(Func.toLongList(ids)));
    }


}
