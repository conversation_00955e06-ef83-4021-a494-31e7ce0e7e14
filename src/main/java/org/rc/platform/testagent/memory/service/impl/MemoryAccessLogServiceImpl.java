package org.rc.platform.testagent.memory.service.impl;


import org.rc.platform.testagent.memory.entity.MemoryAccessLog;
import org.rc.platform.testagent.memory.mapper.MemoryAccessLogMapper;
import org.rc.platform.testagent.memory.vo.MemoryAccessLogVO;
import org.rc.platform.testagent.memory.service.MemoryAccessLogService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆访问记录表(MemoryAccessLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:51
 */
@Service
public class MemoryAccessLogServiceImpl extends BaseServiceImpl<MemoryAccessLogMapper, MemoryAccessLog> implements MemoryAccessLogService {

    @Override
    public IPage<MemoryAccessLogVO> selectMemoryAccessLogPage(IPage<MemoryAccessLogVO> page, MemoryAccessLogVO memoryAccessLog) {
        return page.setRecords(baseMapper.selectMemoryAccessLogPage(page, memoryAccessLog));
    }

}
