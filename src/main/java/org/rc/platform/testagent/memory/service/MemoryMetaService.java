package org.rc.platform.testagent.memory.service;


import org.rc.platform.testagent.memory.entity.MemoryMeta;
import org.rc.platform.testagent.memory.vo.MemoryMetaVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 记忆体元数据(MemoryMeta)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:53
 */
public interface MemoryMetaService extends BaseService<MemoryMeta> {

    /**
     * 自定义分页
     *
     * @param page
     * @param memoryMeta
     * @return
     */
    IPage<MemoryMetaVO> selectMemoryMetaPage(IPage<MemoryMetaVO> page, MemoryMetaVO memoryMeta);
}
