package org.rc.platform.testagent.memory.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.memory.entity.MemoryMeta;

import org.rc.platform.testagent.memory.vo.MemoryMetaVO;
import org.rc.platform.testagent.memory.wrapper.MemoryMetaWrapper;


import org.rc.platform.testagent.memory.service.MemoryMetaService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 记忆体元数据(MemoryMeta)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:10:53
 */
@RestController
@RequestMapping("/memoryMeta")
@Tag(name = "MemoryMeta", description = "记忆体元数据接口")
public class MemoryMetaController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private MemoryMetaService memoryMetaService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入memoryMeta")
    public R<MemoryMetaVO> detail(MemoryMeta memoryMeta) {
        MemoryMeta detail = memoryMetaService.getOne(Condition.getQueryWrapper(memoryMeta));
        return R.data(MemoryMetaWrapper.build().entityVO(detail));
    }

    /**
     * 分页 memoryMeta
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入memoryMeta")
    public R<IPage<MemoryMetaVO>> list(MemoryMeta memoryMeta, Query query) {
        IPage<MemoryMeta> pages = memoryMetaService.page(Condition.getPage(query), Condition.getQueryWrapper(memoryMeta));
        return R.data(MemoryMetaWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 memoryMeta
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入memoryMeta")
    public R<IPage<MemoryMetaVO>> page(MemoryMetaVO memoryMeta, Query query) {
        IPage<MemoryMetaVO> pages = memoryMetaService.selectMemoryMetaPage(Condition.getPage(query), memoryMeta);
        return R.data(pages);
    }

    /**
     * 新增 memoryMeta
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入memoryMeta")
    public R save(@Valid @RequestBody MemoryMeta memoryMeta) {
        return R.status(memoryMetaService.save(memoryMeta));
    }

    /**
     * 修改 memoryMeta
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入memoryMeta")
    public R update(@Valid @RequestBody MemoryMeta memoryMeta) {
        return R.status(memoryMetaService.updateById(memoryMeta));
    }

    /**
     * 新增或修改 memoryMeta
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入memoryMeta")
    public R submit(@Valid @RequestBody MemoryMeta memoryMeta) {
        return R.status(memoryMetaService.saveOrUpdate(memoryMeta));
    }


    /**
     * 删除 memoryMeta
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(memoryMetaService.deleteLogic(Func.toLongList(ids)));
    }


}
