import request from '@/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/demo/memoryAccessLog/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/demo/memoryAccessLog/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/demo/memoryAccessLog/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/demo/memoryAccessLog/submit',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/demo/memoryAccessLog/submit',
        method: 'post',
        data: row
    })
}
