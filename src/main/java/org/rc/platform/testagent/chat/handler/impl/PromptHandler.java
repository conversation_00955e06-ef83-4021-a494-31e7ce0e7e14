package org.rc.platform.testagent.chat.handler.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.rc.platform.testagent.chat.dto.ChatHandleDTO;
import org.rc.platform.testagent.chat.handler.AbstractChatHandler;
import org.rc.platform.testagent.prompt.entity.PromptTemplate;
import org.rc.platform.testagent.prompt.service.PromptTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 提示词处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PromptHandler extends AbstractChatHandler {

    @Autowired
    private PromptTemplateService promptTemplateService;

    @Override
    public int order() {
        return 250;
    }

    @Override
    public boolean execute(ChatHandleDTO handleDTO) {
        // 所有请求都需要检查是否有提示词
        return true;
    }

    @Override
    protected boolean doRun(ChatHandleDTO handleDTO) {
        log.info("进入提示词处理器");

        // 检查是否有提示词代码
        if (handleDTO.getReqDTO().getDialogueInput() != null && 
            StringUtils.hasText(handleDTO.getReqDTO().getDialogueInput().getPrompt())) {
            
            String promptCode = handleDTO.getReqDTO().getDialogueInput().getPrompt();
            String promptContent = getPromptContentByTplName(promptCode);
            
            if (StringUtils.hasText(promptContent)) {
                handleDTO.setSystemPrompt(promptContent);
                log.info("成功设置系统提示词，tpl_name: {}, content长度: {}", 
                        promptCode, promptContent.length());
            } else {
                log.warn("未能获取到提示词内容，tpl_name: {}", promptCode);
            }
        } else {
            log.debug("请求中未包含提示词代码");
        }

        log.info("提示词处理完成");
        return true; // 继续执行下一个处理器
    }

    /**
     * 根据模板名称获取提示词内容
     */
    private String getPromptContentByTplName(String tplName) {
        try {
            QueryWrapper<PromptTemplate> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tpl_name", tplName)
                       .eq("is_deleted", 0)
                       .eq("status", 0);

            PromptTemplate promptTemplate = promptTemplateService.getOne(queryWrapper);
            if (promptTemplate != null && StringUtils.hasText(promptTemplate.getContent())) {
                log.info("成功获取提示词模板，tpl_name: {}, content长度: {}", 
                        tplName, promptTemplate.getContent().length());
                return promptTemplate.getContent();
            } else {
                log.warn("未找到提示词模板或内容为空，tpl_name: {}", tplName);
                return null;
            }
        } catch (Exception e) {
            log.error("获取提示词模板异常，tpl_name: {}", tplName, e);
            return null;
        }
    }
}
