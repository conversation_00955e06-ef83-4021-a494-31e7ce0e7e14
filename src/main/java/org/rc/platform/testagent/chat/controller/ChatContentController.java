package org.rc.platform.testagent.chat.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.chat.entity.ChatContent;

import org.rc.platform.testagent.chat.vo.ChatContentVO;
import org.rc.platform.testagent.chat.wrapper.ChatContentWrapper;


import org.rc.platform.testagent.chat.service.ChatContentService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * AI会话内容表(ChatContent)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@RestController
@RequestMapping("/chatContent")
@Tag(name = "ChatContent", description = "AI会话内容表接口")
public class ChatContentController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ChatContentService chatContentService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入chatContent")
    public R<ChatContentVO> detail(ChatContent chatContent) {
        ChatContent detail = chatContentService.getOne(Condition.getQueryWrapper(chatContent));
        return R.data(ChatContentWrapper.build().entityVO(detail));
    }

    /**
     * 分页 chatContent
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入chatContent")
    public R<IPage<ChatContentVO>> list(ChatContent chatContent, Query query) {
        IPage<ChatContent> pages = chatContentService.page(Condition.getPage(query), Condition.getQueryWrapper(chatContent));
        return R.data(ChatContentWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 chatContent
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入chatContent")
    public R<IPage<ChatContentVO>> page(ChatContentVO chatContent, Query query) {
        IPage<ChatContentVO> pages = chatContentService.selectChatContentPage(Condition.getPage(query), chatContent);
        return R.data(pages);
    }

    /**
     * 新增 chatContent
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入chatContent")
    public R save(@Valid @RequestBody ChatContent chatContent) {
        return R.status(chatContentService.save(chatContent));
    }

    /**
     * 修改 chatContent
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入chatContent")
    public R update(@Valid @RequestBody ChatContent chatContent) {
        return R.status(chatContentService.updateById(chatContent));
    }

    /**
     * 新增或修改 chatContent
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入chatContent")
    public R submit(@Valid @RequestBody ChatContent chatContent) {
        return R.status(chatContentService.saveOrUpdate(chatContent));
    }


    /**
     * 删除 chatContent
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(chatContentService.deleteLogic(Func.toLongList(ids)));
    }


}
