package org.rc.platform.testagent.chat.service.impl;


import org.rc.platform.testagent.chat.entity.ChatConversation;
import org.rc.platform.testagent.chat.mapper.ChatConversationMapper;
import org.rc.platform.testagent.chat.vo.ChatConversationVO;
import org.rc.platform.testagent.chat.service.ChatConversationService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI会话信息表(ChatConversation)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@Service
public class ChatConversationServiceImpl extends BaseServiceImpl<ChatConversationMapper, ChatConversation> implements ChatConversationService {

    @Override
    public IPage<ChatConversationVO> selectChatConversationPage(IPage<ChatConversationVO> page, ChatConversationVO chatConversation) {
        return page.setRecords(baseMapper.selectChatConversationPage(page, chatConversation));
    }

}
