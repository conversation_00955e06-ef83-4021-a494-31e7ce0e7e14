package org.rc.platform.testagent.chat.vo;


import org.rc.platform.testagent.chat.entity.ConversationMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 对话元数据表(ConversationMeta)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对话元数据表")
public class ConversationMetaVO extends ConversationMeta {
    private static final long serialVersionUID = 1L;

}

