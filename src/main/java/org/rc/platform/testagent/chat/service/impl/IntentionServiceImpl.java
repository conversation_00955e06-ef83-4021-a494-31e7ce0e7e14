package org.rc.platform.testagent.chat.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.rc.platform.testagent.chat.dto.IntentionReqDTO;
import org.rc.platform.testagent.chat.dto.IntentionRespDTO;
import org.rc.platform.testagent.chat.service.IntentionService;
import org.rc.platform.testagent.chat.util.HttpUtil;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 */
public class IntentionServiceImpl implements IntentionService {

    @Value("${intention.base-url}")
    private String baseUrl;

    @Value("${intention.intention-url}")
    private String intentionUrl;

    @Override
    public IntentionRespDTO intention(IntentionReqDTO req) {

        String url = baseUrl + intentionUrl;
        return HttpUtil.post(url, JSONObject.toJSONString(req), null,IntentionRespDTO.class);
    }

}
