package org.rc.platform.testagent.chat.handler;

import lombok.extern.slf4j.Slf4j;
import org.rc.platform.testagent.chat.dto.ChatHandleDTO;
import org.rc.platform.testagent.chat.service.ChatContentService;
import org.rc.platform.testagent.chat.service.ChatConversationService;
import org.rc.platform.testagent.chat.service.LlmService;
import org.rc.platform.testagent.memory.service.MemoryService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 聊天处理器抽象基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractChatHandler {

    @Autowired
    protected ChatConversationService chatConversationService;

    @Autowired
    protected ChatContentService chatContentService;

    @Autowired
    protected LlmService llmService;

    @Autowired
    protected MemoryService memoryService;

    /**
     * 处理器执行顺序，数值越小优先级越高
     */
    public abstract int order();

    /**
     * 判断是否需要执行当前处理器
     *
     * @param handleDTO 处理数据对象
     * @return true-需要执行，false-跳过
     */
    public abstract boolean execute(ChatHandleDTO handleDTO);

    /**
     * 执行具体的处理逻辑
     *
     * @param handleDTO 处理数据对象
     * @return true-继续执行下一个处理器，false-终止责任链
     */
    public boolean run(ChatHandleDTO handleDTO) {
        try {
            long startTime = System.currentTimeMillis();
            boolean result = doRun(handleDTO);
            long endTime = System.currentTimeMillis();
            log.info("处理器 {} 执行耗时: {}ms", getHandlerName(), endTime - startTime);
            return result;
        } catch (Exception e) {
            log.error("处理器 {} 执行异常", getHandlerName(), e);
            handleDTO.setExceptionAndComplete(e);
            return false; // 异常时终止责任链
        }
    }

    /**
     * 子类实现具体的处理逻辑
     *
     * @param handleDTO 处理数据对象
     * @return true-继续执行下一个处理器，false-终止责任链
     */
    protected abstract boolean doRun(ChatHandleDTO handleDTO);

    /**
     * 处理器名称，用于日志输出
     */
    public String getHandlerName() {
        return this.getClass().getSimpleName();
    }
}
