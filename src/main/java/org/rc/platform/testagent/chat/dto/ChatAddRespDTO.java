package org.rc.platform.testagent.chat.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 大模型对话响应DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "大模型对话响应")
public class ChatAddRespDTO {

    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "内容ID")
    private String contentId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "流式响应结果")
    private FlowResultDTO flowResult;

    @Data
    @Schema(description = "流式响应结果")
    public static class FlowResultDTO {

        @Schema(description = "流式响应编号")
        private Integer index;

        @Schema(description = "输出内容")
        private String outContent;

        @Schema(description = "思考过程")
        private String reasoningContent;

        @Schema(description = "模型类型")
        private String modelType;

        @Schema(description = "完成原因")
        private String finishReason;

        @Schema(description = "输出时间")
        private String outputTime;
    }
}