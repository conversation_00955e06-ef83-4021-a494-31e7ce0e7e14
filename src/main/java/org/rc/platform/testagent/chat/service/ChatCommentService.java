package org.rc.platform.testagent.chat.service;


import org.rc.platform.testagent.chat.entity.ChatComment;
import org.rc.platform.testagent.chat.vo.ChatCommentVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI用户评价结果(ChatComment)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:38
 */
public interface ChatCommentService extends BaseService<ChatComment> {

    /**
     * 自定义分页
     *
     * @param page
     * @param chatComment
     * @return
     */
    IPage<ChatCommentVO> selectChatCommentPage(IPage<ChatCommentVO> page, ChatCommentVO chatComment);
}
