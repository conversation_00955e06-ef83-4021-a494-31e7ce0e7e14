package org.rc.platform.testagent.chat.service.impl;


import org.rc.platform.testagent.chat.entity.ConversationMeta;
import org.rc.platform.testagent.chat.mapper.ConversationMetaMapper;
import org.rc.platform.testagent.chat.vo.ConversationMetaVO;
import org.rc.platform.testagent.chat.service.ConversationMetaService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 对话元数据表(ConversationMeta)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
@Service
public class ConversationMetaServiceImpl extends BaseServiceImpl<ConversationMetaMapper, ConversationMeta> implements ConversationMetaService {

    @Override
    public IPage<ConversationMetaVO> selectConversationMetaPage(IPage<ConversationMetaVO> page, ConversationMetaVO conversationMeta) {
        return page.setRecords(baseMapper.selectConversationMetaPage(page, conversationMeta));
    }

}
