package org.rc.platform.testagent.chat.service.impl;


import org.rc.platform.testagent.chat.entity.ChatContent;
import org.rc.platform.testagent.chat.mapper.ChatContentMapper;
import org.rc.platform.testagent.chat.vo.ChatContentVO;
import org.rc.platform.testagent.chat.service.ChatContentService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI会话内容表(ChatContent)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@Service
public class ChatContentServiceImpl extends BaseServiceImpl<ChatContentMapper, ChatContent> implements ChatContentService {

    @Override
    public IPage<ChatContentVO> selectChatContentPage(IPage<ChatContentVO> page, ChatContentVO chatContent) {
        return page.setRecords(baseMapper.selectChatContentPage(page, chatContent));
    }

}
