package org.rc.platform.testagent.chat.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.chat.entity.ChatContent;

import org.rc.platform.testagent.chat.vo.ChatContentVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI会话内容表(ChatContent)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
public interface ChatContentMapper extends BaseMapper<ChatContent> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ChatContent> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ChatContent> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ChatContent> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ChatContent> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param chatContent
     * @return
     */
    List<ChatContentVO> selectChatContentPage(IPage page, ChatContentVO chatContent);

}
