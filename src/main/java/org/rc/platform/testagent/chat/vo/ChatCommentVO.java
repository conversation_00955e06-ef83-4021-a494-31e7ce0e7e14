package org.rc.platform.testagent.chat.vo;


import org.rc.platform.testagent.chat.entity.ChatComment;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI用户评价结果(ChatComment)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI用户评价结果")
public class ChatCommentVO extends ChatComment {
    private static final long serialVersionUID = 1L;

}

