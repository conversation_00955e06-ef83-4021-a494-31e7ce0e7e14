package org.rc.platform.testagent.chat.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class UserAreaAuthHeaderUtil {


    private static final SecureRandom secureRandom = new SecureRandom();


    public static Map<String, String> getUserAppHead(String appkey, String appSecretId, String appSecret) {
        Map<String, String> headers = UserAreaAuthHeaderUtil.generateHeader(appkey,
                appSecretId, appSecret, UUID.randomUUID().toString(),
                "1.0");
        return headers;
    }

    public static Map<String, String> generateHeader(String appKey, String appSecretId, String appSecret, String tid, String algorithmVersion) {
        String yunRand = calculateRand();
        return generateHeader(appKey, appSecretId, appSecret, tid, yunRand, algorithmVersion);
    }

    public static Map<String, String> generateHeader(String appKey, String appSecretId, String appSecret, String tid, String yunRand, String algorithmVersion) {
        String yunKeyAndSecretId = appKey + "|" + appSecretId;
        String yunAuth = getSign(yunKeyAndSecretId, appSecret, yunRand, tid);

        Map<String, String> headers = new HashMap<>();

        headers.put("x-yun-app-key", yunKeyAndSecretId);
        headers.put("x-yun-neauth", yunAuth);
        headers.put("x-yun-neauth-version", algorithmVersion);
        headers.put("x-yun-rand", yunRand);
        headers.put("x-yun-tid", tid);
        return headers;
    }

    public static String getSign(String appKey, String appSecret, String rand, String tid) {
        byte[] randBytes = rand.getBytes(StandardCharsets.UTF_8);
        if (randBytes.length < 16) {
            randBytes = String.format("%016d%s", 0, rand).getBytes(StandardCharsets.UTF_8);
        }

        byte[] iv = new byte[16];
        System.arraycopy(randBytes, randBytes.length - 16, iv, 0, 16);

        String msg = String.format("x-yun-app-key=%s&x-yun-rand=%s&x-yun-tid=%s", appKey, rand, tid).toUpperCase();
        SecretKeySpec secretKeySpec = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), "AES");

        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivSpec);
            byte[] cipherText = cipher.doFinal(msg.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(cipherText);
        } catch (Exception e) {
            return null;
        }
    }

    private static String calculateRand() {
        int randomInt = secureRandom.nextInt(900000) + 100000;
        return randomInt + String.valueOf(System.currentTimeMillis());
    }

}