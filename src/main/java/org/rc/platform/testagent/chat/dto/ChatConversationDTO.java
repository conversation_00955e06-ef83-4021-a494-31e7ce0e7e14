package org.rc.platform.testagent.chat.dto;


import org.rc.platform.testagent.chat.entity.ChatConversation;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI会话信息表(ChatConversation)数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatConversationDTO extends ChatConversation {
    private static final long serialVersionUID = 1L;

}

