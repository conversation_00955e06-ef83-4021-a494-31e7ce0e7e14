package org.rc.platform.testagent.chat.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 大模型API响应对象
 *
 * <AUTHOR>
 */
@Data
public class LlmChatRespBean {

    private Boolean success;
    private String code;
    private String message;
    private DataDTO data;

    @Data
    public static class DataDTO {
        private List<ChoiceDTO> choices;
        private Map<String, Object> usage;
        private List<Object> searchResults;
    }

    @Data
    public static class ChoiceDTO {
        private Integer index;
        private MessageDTO message;
        private String finishResion; // 注意：字段名与API返回保持一致
    }

    @Data
    public static class MessageDTO {
        private String name;
        private String role;
        private String content;
        private String reasoningContent;
    }
}