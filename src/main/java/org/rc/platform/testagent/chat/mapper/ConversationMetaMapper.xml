<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.chat.mapper.ConversationMetaMapper">

    <resultMap type="org.rc.platform.testagent.chat.entity.ConversationMeta" id="conversationMetaResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="conversationId" column="conversation_id" jdbcType="INTEGER"/>
        <result property="metaKey" column="meta_key" jdbcType="VARCHAR"/>
        <result property="metaValue" column="meta_value" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_conversation_meta(conversation_id, meta_key, meta_value, user_id, status, create_user,
        update_user, create_time, update_time, is_deleted, remark, sort, tenant_id, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.metaKey}, #{entity.metaValue}, #{entity.userId}, #{entity.status},
            #{entity.createUser}, #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted},
            #{entity.remark}, #{entity.sort}, #{entity.tenantId}, #{entity.version})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_conversation_meta(conversation_id, meta_key, meta_value, user_id, status, create_user,
        update_user, create_time, update_time, is_deleted, remark, sort, tenant_id, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.metaKey}, #{entity.metaValue}, #{entity.userId}, #{entity.status},
            #{entity.createUser}, #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted},
            #{entity.remark}, #{entity.sort}, #{entity.tenantId}, #{entity.version})
        </foreach>
        on duplicate key update
        conversation_id = values(conversation_id) , meta_key = values(meta_key) , meta_value = values(meta_value) ,
        user_id = values(user_id) , status = values(status) , create_user = values(create_user) , update_user =
        values(update_user) , create_time = values(create_time) , update_time = values(update_time) , is_deleted =
        values(is_deleted) , remark = values(remark) , sort = values(sort) , tenant_id = values(tenant_id) , version =
        values(version)
    </insert>
    <select id="selectConversationMetaPage" resultMap="conversationMetaResultMap">
        select *
        from ai_conversation_meta
        where is_deleted = 0
    </select>

</mapper>
