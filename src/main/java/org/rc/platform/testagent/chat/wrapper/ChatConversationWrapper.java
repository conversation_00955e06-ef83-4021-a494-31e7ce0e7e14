/**
 * richinfo
 */


package org.rc.platform.testagent.chat.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.chat.entity.ChatConversation;
import org.rc.platform.testagent.chat.vo.ChatConversationVO;


/**
 * AI会话信息表(ChatConversation)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
public class ChatConversationWrapper extends BaseEntityWrapper<ChatConversation, ChatConversationVO> {

    public static ChatConversationWrapper build() {
        return new ChatConversationWrapper();
    }

    @Override
    public ChatConversationVO entityVO(ChatConversation chatConversation) {
        ChatConversationVO chatConversationVO = BeanUtil.copyProperties(chatConversation, ChatConversationVO.class);

        return chatConversationVO;
    }

}

