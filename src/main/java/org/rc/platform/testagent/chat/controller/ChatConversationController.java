package org.rc.platform.testagent.chat.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.chat.entity.ChatConversation;

import org.rc.platform.testagent.chat.vo.ChatConversationVO;
import org.rc.platform.testagent.chat.wrapper.ChatConversationWrapper;


import org.rc.platform.testagent.chat.service.ChatConversationService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * AI会话信息表(ChatConversation)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
@RestController
@RequestMapping("/chatConversation")
@Tag(name = "ChatConversation", description = "AI会话信息表接口")
public class ChatConversationController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ChatConversationService chatConversationService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入chatConversation")
    public R<ChatConversationVO> detail(ChatConversation chatConversation) {
        ChatConversation detail = chatConversationService.getOne(Condition.getQueryWrapper(chatConversation));
        return R.data(ChatConversationWrapper.build().entityVO(detail));
    }

    /**
     * 分页 chatConversation
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入chatConversation")
    public R<IPage<ChatConversationVO>> list(ChatConversation chatConversation, Query query) {
        IPage<ChatConversation> pages = chatConversationService.page(Condition.getPage(query), Condition.getQueryWrapper(chatConversation));
        return R.data(ChatConversationWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 chatConversation
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入chatConversation")
    public R<IPage<ChatConversationVO>> page(ChatConversationVO chatConversation, Query query) {
        IPage<ChatConversationVO> pages = chatConversationService.selectChatConversationPage(Condition.getPage(query), chatConversation);
        return R.data(pages);
    }

    /**
     * 新增 chatConversation
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入chatConversation")
    public R save(@Valid @RequestBody ChatConversation chatConversation) {
        return R.status(chatConversationService.save(chatConversation));
    }

    /**
     * 修改 chatConversation
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入chatConversation")
    public R update(@Valid @RequestBody ChatConversation chatConversation) {
        return R.status(chatConversationService.updateById(chatConversation));
    }

    /**
     * 新增或修改 chatConversation
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入chatConversation")
    public R submit(@Valid @RequestBody ChatConversation chatConversation) {
        return R.status(chatConversationService.saveOrUpdate(chatConversation));
    }


    /**
     * 删除 chatConversation
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(chatConversationService.deleteLogic(Func.toLongList(ids)));
    }


}
