/**
 * richinfo
 */


package org.rc.platform.testagent.chat.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.chat.entity.ConversationMeta;
import org.rc.platform.testagent.chat.vo.ConversationMetaVO;


/**
 * 对话元数据表(ConversationMeta)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
public class ConversationMetaWrapper extends BaseEntityWrapper<ConversationMeta, ConversationMetaVO> {

    public static ConversationMetaWrapper build() {
        return new ConversationMetaWrapper();
    }

    @Override
    public ConversationMetaVO entityVO(ConversationMeta conversationMeta) {
        ConversationMetaVO conversationMetaVO = BeanUtil.copyProperties(conversationMeta, ConversationMetaVO.class);

        return conversationMetaVO;
    }

}

