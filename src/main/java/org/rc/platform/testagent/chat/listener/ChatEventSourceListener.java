package org.rc.platform.testagent.chat.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.rc.platform.testagent.chat.dto.ChatAddRespDTO;
import org.rc.platform.testagent.chat.dto.ChatHandleDTO;
import org.rc.platform.testagent.chat.dto.LlmChatRespBean;
import org.rc.platform.testagent.chat.entity.ChatContent;
import org.rc.platform.testagent.chat.service.ChatContentService;
import org.rc.platform.testagent.chat.util.SpringContextUtil;
import org.rc.platform.testagent.memory.entity.Memory;
import org.rc.platform.testagent.memory.service.MemoryService;
import org.richinfo.framework.core.tool.api.R;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 大模型流式响应监听器，支持数据库更新
 *
 * <AUTHOR>
 */
@Slf4j
public class ChatEventSourceListener extends EventSourceListener {

    private final SseEmitter sseEmitter;
    private final String conversationId;
    private final String contentId;
    private final String modelType;
    private final ObjectMapper objectMapper = new ObjectMapper();

    // Database update related fields
    private ChatContentService chatContentService;
    private MemoryService memoryService;
    private final Long contentIdLong;
    private StringBuilder fullContent = new StringBuilder();
    private StringBuilder reasoningContent = new StringBuilder();
    private boolean hasStartedNormalContent = false;

    public ChatEventSourceListener(SseEmitter sseEmitter, String conversationId, String contentId, String modelType) {
        this(sseEmitter, conversationId, contentId, modelType, null);
    }

    public ChatEventSourceListener(SseEmitter sseEmitter, String conversationId, String contentId, String modelType, ChatContentService chatContentService) {
        this.sseEmitter = sseEmitter;
        this.conversationId = conversationId;
        this.contentId = contentId;
        this.modelType = modelType;
        this.chatContentService = chatContentService;
        this.contentIdLong = contentId != null ? Long.parseLong(contentId) : null;
    }

    /**
     * 基于 ChatHandleDTO 创建监听器的便捷构造函数
     * 使用 SpringContextUtil 自动获取 ChatContentService
     *
     * @param handleDTO 聊天处理数据对象
     */
    public ChatEventSourceListener(ChatHandleDTO handleDTO) {
        this.sseEmitter = handleDTO.getSseEmitter();
        this.conversationId = handleDTO.getConversationId() != null ? handleDTO.getConversationId().toString() : null;
        this.contentId = handleDTO.getContentId() != null ? handleDTO.getContentId().toString() : null;
        this.modelType = handleDTO.getReqDTO().getModelType();
        this.contentIdLong = handleDTO.getContentId();
        // 使用 SpringContextUtil 获取服务
        this.chatContentService = SpringContextUtil.getBean(ChatContentService.class);
        this.memoryService = SpringContextUtil.getBean(MemoryService.class);
    }

    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("SSE connection opened");
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        try {
            LlmChatRespBean llmResp = objectMapper.readValue(data, LlmChatRespBean.class);

            if (llmResp.getSuccess() && llmResp.getData() != null &&
                !llmResp.getData().getChoices().isEmpty()) {

                LlmChatRespBean.ChoiceDTO choice = llmResp.getData().getChoices().get(0);

                ChatAddRespDTO respDTO = new ChatAddRespDTO();
                respDTO.setConversationId(conversationId);
                respDTO.setContentId(contentId);
                respDTO.setTitle("");

                ChatAddRespDTO.FlowResultDTO flowResult = new ChatAddRespDTO.FlowResultDTO();
                flowResult.setIndex(choice.getIndex());
                flowResult.setOutContent(choice.getMessage().getContent());
                flowResult.setReasoningContent(choice.getMessage().getReasoningContent());
                flowResult.setModelType(modelType);

                // Use actual finishReason, default to "processing" if empty
                String finishReason = choice.getFinishResion();
                if (finishReason == null || finishReason.isEmpty()) {
                    finishReason = "processing";
                }
                flowResult.setFinishReason(finishReason);
                flowResult.setOutputTime(ZonedDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));

                respDTO.setFlowResult(flowResult);

                // 将响应包装在R中
                R<ChatAddRespDTO> response = R.data(respDTO);
                sseEmitter.send(SseEmitter.event().data(response));

                // Async update database
                updateContentAsync(data);

                // Check if completed: end stream when finishReason is "stop"
                if ("stop".equals(finishReason)) {
                    log.info("流式响应完成，finishReason: {}", finishReason);
                    sseEmitter.complete();
                }
            }
        } catch (Exception e) {
            log.error("处理SSE事件异常", e);
            sseEmitter.completeWithError(e);
        }
    }

    @Override
    public void onClosed(EventSource eventSource) {
        log.info("SSE connection closed");
        // Final database update
        finalUpdateContent();
        sseEmitter.complete();
    }

    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        log.error("SSE connection failed", t);
        sseEmitter.completeWithError(t);
    }

    /**
     * Async update database content
     */
    private void updateContentAsync(String data) {
        if (contentIdLong == null) {
            return;
        }

        // 懒加载获取 ChatContentService
        if (chatContentService == null) {
            try {
                chatContentService = SpringContextUtil.getBean(ChatContentService.class);
            } catch (Exception e) {
                log.error("无法获取 ChatContentService，跳过数据库更新", e);
                return;
            }
        }

        try {
            // Parse response data and accumulate content
            LlmChatRespBean llmResp = objectMapper.readValue(data, LlmChatRespBean.class);

            if (llmResp.getSuccess() && llmResp.getData() != null &&
                !llmResp.getData().getChoices().isEmpty()) {

                LlmChatRespBean.ChoiceDTO choice = llmResp.getData().getChoices().get(0);

                // 处理reasoningContent（思维链内容）
                if (choice.getMessage() != null && choice.getMessage().getReasoningContent() != null) {
                    String reasoning = choice.getMessage().getReasoningContent();
                    if (!reasoning.isEmpty()) {
                        // 如果是第一次接收到reasoningContent，添加开始标签
                        if (reasoningContent.length() == 0) {
                            reasoningContent.append("<think>");
                        }
                        reasoningContent.append(reasoning);
                    }
                }

                // 处理普通content
                if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                    String content = choice.getMessage().getContent();
                    if (!content.isEmpty()) {
                        // 如果是第一次接收到普通内容，且之前有reasoningContent，则先关闭thinking标签
                        if (!hasStartedNormalContent && reasoningContent.length() > 0) {
                            reasoningContent.append("</think>");
                            fullContent.append(reasoningContent.toString());
                            hasStartedNormalContent = true;
                        }
                        fullContent.append(content);
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新内容异常，contentId: {}", contentIdLong, e);
        }
    }

    /**
     * Final update database content
     */
    private void finalUpdateContent() {
        if (contentIdLong == null) {
            return;
        }

        // 懒加载获取服务
        if (chatContentService == null) {
            try {
                chatContentService = SpringContextUtil.getBean(ChatContentService.class);
                memoryService = SpringContextUtil.getBean(MemoryService.class);
            } catch (Exception e) {
                log.error("无法获取服务，跳过最终数据库更新", e);
                return;
            }
        }

        try {
            // 如果只有reasoningContent没有普通content，需要关闭thinking标签
            if (reasoningContent.length() > 0 && !hasStartedNormalContent) {
                reasoningContent.append("</think>");
                fullContent.append(reasoningContent.toString());
            }

            ChatContent content = chatContentService.getById(contentIdLong);
            if (content != null) {
                content.setOutContent(fullContent.toString());
                content.setOutContentTime(new Date());
                content.setUpdateTime(new Date());
                chatContentService.updateById(content);
                log.info("最终更新内容成功，contentId: {}, 内容长度: {}", contentIdLong, fullContent.length());

                // 保存助手回复到记忆表
                saveAssistantReplyToMemory(content.getConversationId(), fullContent.toString(), content.getUserId());
            }
        } catch (Exception e) {
            log.error("最终更新内容异常，contentId: {}", contentIdLong, e);
        }
    }

    /**
     * 保存助手回复到记忆表
     */
    private void saveAssistantReplyToMemory(Long conversationId, String content, Long userId) {
        try {
            if (memoryService != null && conversationId != null && content != null && !content.trim().isEmpty()) {
                Memory memory = new Memory();
                memory.setConversationId(conversationId);
                memory.setRole("assistant");
                memory.setContent(content);
                memory.setMemoryType(0); // 0表示会话记忆
                memory.setIsCritical(0); // 默认非关键
                memory.setImportanceScore(0.5); // 默认重要性评分
                memory.setRelevanceScore(0.5); // 默认相关性评分
                memory.setStatus(0); // 正常状态
                memory.setIsDeleted(0); // 未删除
                memory.setTenantId(0L); // 默认租户
                memory.setCreateUser(userId != null ? userId : 1222477271620092911L);
                memory.setUpdateUser(userId != null ? userId : 1222477271620092911L);
                memory.setCreateTime(new Date());
                memory.setUpdateTime(new Date());
                memory.setVersion(0);

                memoryService.save(memory);
                log.info("保存助手回复到记忆表成功，conversationId: {}, content长度: {}",
                        conversationId, content.length());
            }
        } catch (Exception e) {
            log.error("保存助手回复到记忆表异常，conversationId: {}", conversationId, e);
        }
    }
}