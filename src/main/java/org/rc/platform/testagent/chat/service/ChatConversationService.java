package org.rc.platform.testagent.chat.service;


import org.rc.platform.testagent.chat.entity.ChatConversation;
import org.rc.platform.testagent.chat.vo.ChatConversationVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI会话信息表(ChatConversation)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
public interface ChatConversationService extends BaseService<ChatConversation> {

    /**
     * 自定义分页
     *
     * @param page
     * @param chatConversation
     * @return
     */
    IPage<ChatConversationVO> selectChatConversationPage(IPage<ChatConversationVO> page, ChatConversationVO chatConversation);
}
