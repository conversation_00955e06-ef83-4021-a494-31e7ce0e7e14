package org.rc.platform.testagent.chat.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.chat.entity.ChatConversation;

import org.rc.platform.testagent.chat.vo.ChatConversationVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI会话信息表(ChatConversation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
public interface ChatConversationMapper extends BaseMapper<ChatConversation> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ChatConversation> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ChatConversation> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ChatConversation> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ChatConversation> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param chatConversation
     * @return
     */
    List<ChatConversationVO> selectChatConversationPage(IPage page, ChatConversationVO chatConversation);

}
