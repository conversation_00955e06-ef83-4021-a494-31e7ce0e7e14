package org.rc.platform.testagent.chat.handler.impl;

import lombok.extern.slf4j.Slf4j;
import org.rc.platform.testagent.chat.dto.ChatHandleDTO;
import org.rc.platform.testagent.chat.handler.AbstractChatHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 意图识别处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IntentionHandler extends AbstractChatHandler {

    @Override
    public int order() {
        return 200;
    }

    @Override
    public boolean execute(ChatHandleDTO handleDTO) {
        // 所有请求都需要意图识别
        return true;
    }

    @Override
    protected boolean doRun(ChatHandleDTO handleDTO) {
        log.info("进入意图识别处理器");

        // 获取用户输入的指令
        String command = null;
        if (handleDTO.getReqDTO().getDialogueInput() != null) {
            command = handleDTO.getReqDTO().getDialogueInput().getIntention();
        }

        // 简单的意图识别逻辑
        String intentionCode = recognizeIntention(
                handleDTO.getReqDTO().getDialogueInput().getDialogue(),
                command
        );

        handleDTO.setIntentionCode(intentionCode);
        if ("TEST_REQUIREMENT".equals(intentionCode)) {
            handleDTO.getReqDTO().getDialogueInput().setPrompt("test_requirement_prompt");
        }
        log.info("意图识别完成，intentionCode: {}", intentionCode);
        return true; // 继续执行下一个处理器
    }

    /**
     * 简单的意图识别逻辑，todo 调用云盘算法中心的能力
     */
    private String recognizeIntention(String content, String command) {
        // 如果有明确的指令，直接使用
        if (StringUtils.hasText(command)) {
            return command;
        }

        // 简单的关键词匹配 todo 调用云盘算法中心的能力
        String lowerContent = content.toLowerCase();

        // 默认为普通对话
        return "TEST_REQUIREMENT";
    }

}
