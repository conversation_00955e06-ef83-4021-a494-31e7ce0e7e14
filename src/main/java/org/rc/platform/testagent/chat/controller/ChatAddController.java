package org.rc.platform.testagent.chat.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.rc.platform.testagent.chat.dto.ChatAddReqDTO;
import org.rc.platform.testagent.chat.dto.ChatHandleDTO;
import org.rc.platform.testagent.chat.handler.AbstractChatHandler;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.Valid;
import java.util.Comparator;
import java.util.List;

/**
 * 聊天对话控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/chat")
@Tag(name = "Chat", description = "聊天对话接口")
public class ChatAddController extends RichinfoController {

    @Autowired
    private List<AbstractChatHandler> chatHandlerList;

    /**
     * 初始化处理器列表，按order排序
     */
    @PostConstruct
    private void init() {
        chatHandlerList.sort(Comparator.comparing(AbstractChatHandler::order));
    }


    /**
     * 调用示例：
     * curl --location 'http://localhost:9303/chat/v1/add' \
     * --header 'Content-Type: application/json' \
     * --header 'Accept: text/event-stream' \
     * --data '{
     *     "modelType": "deepseek_r1_32b",
     *     "dialogueInput": {
     *       "dialogue": "当前天气"
     *     }
     *   }'
     */
    @PostMapping("/v1/add")
    @Operation(summary = "大模型对话", description = "流式返回大模型对话结果")
    @Transactional(rollbackFor = Exception.class)
    public SseEmitter add(@Valid @RequestBody ChatAddReqDTO request) {
        log.info("收到聊天请求，userId: {}, conversationId: {}, modelType: {}",
                request.getUserId(), request.getConversationId(), request.getModelType());

        SseEmitter sseEmitter = new SseEmitter(60000L); // 60秒超时
        ChatHandleDTO handleDTO = new ChatHandleDTO(request, sseEmitter);

        try {
            // 执行责任链处理
            for (AbstractChatHandler handler : chatHandlerList) {
                if (handler.execute(handleDTO) && !handler.run(handleDTO)) {
                    break; // 处理器返回false，终止责任链
                }

                if (handleDTO.isCompleted()) {
                    break; // 处理完成，终止责任链
                }
            }

            // 检查是否有异常
            if (handleDTO.getException() != null) {
                sseEmitter.completeWithError(handleDTO.getException());
            }

        } catch (Exception e) {
            log.error("处理聊天请求异常", e);
            sseEmitter.completeWithError(e);
        }
        return sseEmitter;
    }
}
