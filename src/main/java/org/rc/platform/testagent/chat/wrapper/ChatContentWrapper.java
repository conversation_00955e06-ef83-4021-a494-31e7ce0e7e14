/**
 * richinfo
 */


package org.rc.platform.testagent.chat.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.chat.entity.ChatContent;
import org.rc.platform.testagent.chat.vo.ChatContentVO;


/**
 * AI会话内容表(ChatContent)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
public class ChatContentWrapper extends BaseEntityWrapper<ChatContent, ChatContentVO> {

    public static ChatContentWrapper build() {
        return new ChatContentWrapper();
    }

    @Override
    public ChatContentVO entityVO(ChatContent chatContent) {
        ChatContentVO chatContentVO = BeanUtil.copyProperties(chatContent, ChatContentVO.class);

        return chatContentVO;
    }

}

