<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.chat.mapper.ChatContentMapper">

    <resultMap type="org.rc.platform.testagent.chat.entity.ChatContent" id="chatContentResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="conversationId" column="conversation_id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="modelType" column="model_type" jdbcType="VARCHAR"/>
        <result property="commandType" column="command_type" jdbcType="INTEGER"/>
        <result property="toolsCommand" column="tools_command" jdbcType="INTEGER"/>
        <result property="inContent" column="in_content" jdbcType="VARCHAR"/>
        <result property="inContentTime" column="in_content_time" jdbcType="TIMESTAMP"/>
        <result property="isCommand" column="is_command" jdbcType="VARCHAR"/>
        <result property="commandContent" column="command_content" jdbcType="VARCHAR"/>
        <result property="userConfirmation" column="user_confirmation" jdbcType="VARCHAR"/>
        <result property="confirmationTime" column="confirmation_time" jdbcType="TIMESTAMP"/>
        <result property="outContentTime" column="out_content_time" jdbcType="TIMESTAMP"/>
        <result property="outContent" column="out_content" jdbcType="VARCHAR"/>
        <result property="outContentType" column="out_content_type" jdbcType="INTEGER"/>
        <result property="modelTextSize" column="model_text_size" jdbcType="INTEGER"/>
        <result property="modelInputTokens" column="model_input_tokens" jdbcType="INTEGER"/>
        <result property="modelOutputTokens" column="model_output_tokens" jdbcType="INTEGER"/>
        <result property="prompt" column="prompt" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_chat_content(conversation_id, app_id, user_id, model_type, command_type, tools_command,
        in_content, in_content_time, is_command, command_content, user_confirmation, confirmation_time,
        out_content_time, out_content, out_content_type, model_text_size, model_input_tokens, model_output_tokens,
        prompt, create_time, update_time, is_deleted, tenant_id, version, create_user, update_user, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.appId}, #{entity.userId}, #{entity.modelType}, #{entity.commandType},
            #{entity.toolsCommand}, #{entity.inContent}, #{entity.inContentTime}, #{entity.isCommand},
            #{entity.commandContent}, #{entity.userConfirmation}, #{entity.confirmationTime}, #{entity.outContentTime},
            #{entity.outContent}, #{entity.outContentType}, #{entity.modelTextSize}, #{entity.modelInputTokens},
            #{entity.modelOutputTokens}, #{entity.prompt}, #{entity.createTime}, #{entity.updateTime},
            #{entity.isDeleted}, #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser},
            #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_chat_content(conversation_id, app_id, user_id, model_type, command_type, tools_command,
        in_content, in_content_time, is_command, command_content, user_confirmation, confirmation_time,
        out_content_time, out_content, out_content_type, model_text_size, model_input_tokens, model_output_tokens,
        prompt, create_time, update_time, is_deleted, tenant_id, version, create_user, update_user, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.appId}, #{entity.userId}, #{entity.modelType}, #{entity.commandType},
            #{entity.toolsCommand}, #{entity.inContent}, #{entity.inContentTime}, #{entity.isCommand},
            #{entity.commandContent}, #{entity.userConfirmation}, #{entity.confirmationTime}, #{entity.outContentTime},
            #{entity.outContent}, #{entity.outContentType}, #{entity.modelTextSize}, #{entity.modelInputTokens},
            #{entity.modelOutputTokens}, #{entity.prompt}, #{entity.createTime}, #{entity.updateTime},
            #{entity.isDeleted}, #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser},
            #{entity.status})
        </foreach>
        on duplicate key update
        conversation_id = values(conversation_id) , app_id = values(app_id) , user_id = values(user_id) , model_type =
        values(model_type) , command_type = values(command_type) , tools_command = values(tools_command) , in_content =
        values(in_content) , in_content_time = values(in_content_time) , is_command = values(is_command) ,
        command_content = values(command_content) , user_confirmation = values(user_confirmation) , confirmation_time =
        values(confirmation_time) , out_content_time = values(out_content_time) , out_content = values(out_content) ,
        out_content_type = values(out_content_type) , model_text_size = values(model_text_size) , model_input_tokens =
        values(model_input_tokens) , model_output_tokens = values(model_output_tokens) , prompt = values(prompt) ,
        create_time = values(create_time) , update_time = values(update_time) , is_deleted = values(is_deleted) ,
        tenant_id = values(tenant_id) , version = values(version) , create_user = values(create_user) , update_user =
        values(update_user) , status = values(status)
    </insert>
    <select id="selectChatContentPage" resultMap="chatContentResultMap">
        select *
        from ai_chat_content
        where is_deleted = 0
    </select>

</mapper>
