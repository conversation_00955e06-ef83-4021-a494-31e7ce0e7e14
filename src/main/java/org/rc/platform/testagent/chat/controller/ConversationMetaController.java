package org.rc.platform.testagent.chat.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.chat.entity.ConversationMeta;

import org.rc.platform.testagent.chat.vo.ConversationMetaVO;
import org.rc.platform.testagent.chat.wrapper.ConversationMetaWrapper;


import org.rc.platform.testagent.chat.service.ConversationMetaService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 对话元数据表(ConversationMeta)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:35
 */
@RestController
@RequestMapping("/conversationMeta")
@Tag(name = "ConversationMeta", description = "对话元数据表接口")
public class ConversationMetaController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ConversationMetaService conversationMetaService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入conversationMeta")
    public R<ConversationMetaVO> detail(ConversationMeta conversationMeta) {
        ConversationMeta detail = conversationMetaService.getOne(Condition.getQueryWrapper(conversationMeta));
        return R.data(ConversationMetaWrapper.build().entityVO(detail));
    }

    /**
     * 分页 conversationMeta
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入conversationMeta")
    public R<IPage<ConversationMetaVO>> list(ConversationMeta conversationMeta, Query query) {
        IPage<ConversationMeta> pages = conversationMetaService.page(Condition.getPage(query), Condition.getQueryWrapper(conversationMeta));
        return R.data(ConversationMetaWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 conversationMeta
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入conversationMeta")
    public R<IPage<ConversationMetaVO>> page(ConversationMetaVO conversationMeta, Query query) {
        IPage<ConversationMetaVO> pages = conversationMetaService.selectConversationMetaPage(Condition.getPage(query), conversationMeta);
        return R.data(pages);
    }

    /**
     * 新增 conversationMeta
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入conversationMeta")
    public R save(@Valid @RequestBody ConversationMeta conversationMeta) {
        return R.status(conversationMetaService.save(conversationMeta));
    }

    /**
     * 修改 conversationMeta
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入conversationMeta")
    public R update(@Valid @RequestBody ConversationMeta conversationMeta) {
        return R.status(conversationMetaService.updateById(conversationMeta));
    }

    /**
     * 新增或修改 conversationMeta
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入conversationMeta")
    public R submit(@Valid @RequestBody ConversationMeta conversationMeta) {
        return R.status(conversationMetaService.saveOrUpdate(conversationMeta));
    }


    /**
     * 删除 conversationMeta
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(conversationMetaService.deleteLogic(Func.toLongList(ids)));
    }


}
