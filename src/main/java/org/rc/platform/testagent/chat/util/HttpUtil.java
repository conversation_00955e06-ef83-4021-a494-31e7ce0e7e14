package org.rc.platform.testagent.chat.util;

import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.rc.platform.testagent.chat.dto.IntentionRespDTO;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class HttpUtil {

    public static <T> T post(String url, String json, Map<String, String> header, Class<T> clazz){
        OkHttpClient.Builder builder = new OkHttpClient().newBuilder();
        builder.readTimeout(5, TimeUnit.MINUTES);
        builder.connectTimeout(5, TimeUnit.MINUTES);
        builder.writeTimeout(5, TimeUnit.MINUTES);
        OkHttpClient client = builder.build();
        RequestBody requestBody = RequestBody.create(MediaType.get("application/json; charset=utf-8"), json);
        // 创建Request
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(header))
                .post(requestBody)
                .build();
        // 发送请求并处理响应
        String postJson = "";
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return null;
            }
            if (response.body() != null) {
                postJson = response.body().string();
            }
        } catch (IOException e) {
            return null;
        }
        if (StringUtils.isEmpty(postJson)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(postJson);
        String status = jsonObject.getString("status");
        if(!"0000".equals(status)){
            return null;
        }
        return JSONObject.parseObject(jsonObject.getString("data"), clazz);
    }
}
