package org.rc.platform.testagent.chat.service.impl;


import org.rc.platform.testagent.chat.entity.ChatComment;
import org.rc.platform.testagent.chat.mapper.ChatCommentMapper;
import org.rc.platform.testagent.chat.vo.ChatCommentVO;
import org.rc.platform.testagent.chat.service.ChatCommentService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI用户评价结果(ChatComment)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:38
 */
@Service
public class ChatCommentServiceImpl extends BaseServiceImpl<ChatCommentMapper, ChatComment> implements ChatCommentService {

    @Override
    public IPage<ChatCommentVO> selectChatCommentPage(IPage<ChatCommentVO> page, ChatCommentVO chatComment) {
        return page.setRecords(baseMapper.selectChatCommentPage(page, chatComment));
    }

}
