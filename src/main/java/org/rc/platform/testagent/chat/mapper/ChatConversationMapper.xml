<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.chat.mapper.ChatConversationMapper">

    <resultMap type="org.rc.platform.testagent.chat.entity.ChatConversation" id="chatConversationResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_chat_conversation(app_id, user_id, title, create_time, update_time, is_deleted,
        tenant_id, version, create_user, update_user, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.userId}, #{entity.title}, #{entity.createTime}, #{entity.updateTime},
            #{entity.isDeleted}, #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser},
            #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_chat_conversation(app_id, user_id, title, create_time, update_time, is_deleted,
        tenant_id, version, create_user, update_user, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.userId}, #{entity.title}, #{entity.createTime}, #{entity.updateTime},
            #{entity.isDeleted}, #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser},
            #{entity.status})
        </foreach>
        on duplicate key update
        app_id = values(app_id) , user_id = values(user_id) , title = values(title) , create_time = values(create_time)
        , update_time = values(update_time) , is_deleted = values(is_deleted) , tenant_id = values(tenant_id) , version
        = values(version) , create_user = values(create_user) , update_user = values(update_user) , status =
        values(status)
    </insert>
    <select id="selectChatConversationPage" resultMap="chatConversationResultMap">
        select *
        from ai_chat_conversation
        where is_deleted = 0
    </select>

</mapper>
