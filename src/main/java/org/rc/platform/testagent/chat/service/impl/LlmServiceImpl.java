package org.rc.platform.testagent.chat.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.rc.platform.testagent.chat.client.YunAiLlmApiClient;
import org.rc.platform.testagent.chat.dto.LlmChatReqBean;
import org.rc.platform.testagent.chat.listener.ChatEventSourceListener;
import org.rc.platform.testagent.chat.service.LlmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 大模型服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LlmServiceImpl implements LlmService {

    @Autowired
    private YunAiLlmApiClient yunAiLlmApiClient;

    @Autowired
    @Qualifier("llmChatExecutor")
    private ThreadPoolExecutor llmChatExecutor;

    @Override
    public void callLlmAsync(LlmChatReqBean llmRequest, ChatEventSourceListener listener) {
        llmChatExecutor.submit(() -> {
            try {
                log.info("开始异步调用大模型，userId: {}, model: {}", 
                        llmRequest.getUserId(), llmRequest.getModel());
                
                yunAiLlmApiClient.llmChatSSE(llmRequest, listener);
                
                log.info("大模型调用提交成功，userId: {}", llmRequest.getUserId());
            } catch (Exception e) {
                log.error("异步调用大模型异常，userId: {}", llmRequest.getUserId(), e);
                // 通知监听器发生异常
                if (listener != null) {
                    listener.onFailure(null, e, null);
                }
            }
        });
    }
}
