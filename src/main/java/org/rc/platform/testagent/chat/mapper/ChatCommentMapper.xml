<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.chat.mapper.ChatCommentMapper">

    <resultMap type="org.rc.platform.testagent.chat.entity.ChatComment" id="chatCommentResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="conversationId" column="conversation_id" jdbcType="INTEGER"/>
        <result property="contentId" column="content_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="modelType" column="model_type" jdbcType="VARCHAR"/>
        <result property="isLike" column="is_like" jdbcType="INTEGER"/>
        <result property="defaultComment" column="default_comment" jdbcType="VARCHAR"/>
        <result property="isAccept" column="is_accept" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_chat_comment(conversation_id, content_id, user_id, model_type, is_like, default_comment,
        is_accept, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.contentId}, #{entity.userId}, #{entity.modelType}, #{entity.isLike},
            #{entity.defaultComment}, #{entity.isAccept}, #{entity.createTime}, #{entity.updateTime},
            #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser}, #{entity.isDeleted},
            #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_chat_comment(conversation_id, content_id, user_id, model_type, is_like, default_comment,
        is_accept, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.conversationId}, #{entity.contentId}, #{entity.userId}, #{entity.modelType}, #{entity.isLike},
            #{entity.defaultComment}, #{entity.isAccept}, #{entity.createTime}, #{entity.updateTime},
            #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser}, #{entity.isDeleted},
            #{entity.status})
        </foreach>
        on duplicate key update
        conversation_id = values(conversation_id) , content_id = values(content_id) , user_id = values(user_id) ,
        model_type = values(model_type) , is_like = values(is_like) , default_comment = values(default_comment) ,
        is_accept = values(is_accept) , create_time = values(create_time) , update_time = values(update_time) ,
        tenant_id = values(tenant_id) , version = values(version) , create_user = values(create_user) , update_user =
        values(update_user) , is_deleted = values(is_deleted) , status = values(status)
    </insert>
    <select id="selectChatCommentPage" resultMap="chatCommentResultMap">
        select *
        from ai_chat_comment
        where is_deleted = 0
    </select>

</mapper>
