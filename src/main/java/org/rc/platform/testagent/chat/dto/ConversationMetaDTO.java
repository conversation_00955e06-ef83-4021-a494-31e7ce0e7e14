package org.rc.platform.testagent.chat.dto;


import org.rc.platform.testagent.chat.entity.ConversationMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 对话元数据表(ConversationMeta)数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConversationMetaDTO extends ConversationMeta {
    private static final long serialVersionUID = 1L;

}

