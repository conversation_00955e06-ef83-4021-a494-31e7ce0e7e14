package org.rc.platform.testagent.chat.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 聊天处理责任链数据传输对象
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public class ChatHandleDTO {

    /**
     * 请求参数，数据不可改
     */
    private ChatAddReqDTO reqDTO;

    /**
     * SSE流式对象
     */
    private SseEmitter sseEmitter;

    /**
     * 会话ID，如果没有则需要生成一个
     */
    private Long conversationId;

    /**
     * 内容ID，需要新生成一个
     */
    private Long contentId;

    /**
     * 意图编码
     */
    private String intentionCode;

    /**
     * 系统提示词内容
     */
    private String systemPrompt;

    /**
     * 是否处理完成
     */
    private boolean completed = false;

    /**
     * 异常信息
     */
    private Exception exception;

    public ChatHandleDTO(ChatAddReqDTO reqDTO, SseEmitter sseEmitter) {
        this.reqDTO = reqDTO;
        this.sseEmitter = sseEmitter;
    }

    /**
     * 标记处理完成
     */
    public void markCompleted() {
        this.completed = true;
    }

    /**
     * 设置异常并标记完成
     */
    public void setExceptionAndComplete(Exception exception) {
        this.exception = exception;
        this.completed = true;
    }
}
