package org.rc.platform.testagent.chat.vo;


import org.rc.platform.testagent.chat.entity.ChatContent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI会话内容表(ChatContent)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI会话内容表")
public class ChatContentVO extends ChatContent {
    private static final long serialVersionUID = 1L;

}

