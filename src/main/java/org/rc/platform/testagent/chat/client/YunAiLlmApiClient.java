package org.rc.platform.testagent.chat.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.rc.platform.testagent.chat.dto.LlmChatReqBean;
import org.rc.platform.testagent.chat.util.UserAreaAuthHeaderUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 大模型API客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class YunAiLlmApiClient {

    @Value("${yun.ai.text-llm-url}")
    private String aiTextLlmUrl;

    @Value("${yun.ai.text-llm-appkey}")
    private String aiTextLlmAppkey;

    @Value("${yun.ai.text-llm-app-secret-id}")
    private String aiTextLlmAppSecretId;

    @Value("${yun.ai.text-llm-app-secret}")
    private String aiTextLlmAppSecret;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public void llmChatSSE(LlmChatReqBean req, EventSourceListener listener) {
        OkHttpClient okHttpClient = buildOkHttpClient();

        Map<String, String> heads = UserAreaAuthHeaderUtil.getUserAppHead(aiTextLlmAppkey, aiTextLlmAppSecretId, aiTextLlmAppSecret);
        Headers.Builder builder = new Headers.Builder();
        heads.forEach(builder::add);
        builder.add("x-yun-api-version", "v1");
        builder.add("Content-Type", "application/json");
        Headers headers = builder.build();

        try {
            String jsonBody = objectMapper.writeValueAsString(req);
            streamCompletionsDealing(aiTextLlmUrl, jsonBody, headers, okHttpClient, listener);
            log.info("llmChatSSE|userId:{}|heads={},body={}", req.getUserId(), heads, jsonBody);
        } catch (Exception e) {
            log.error("llmChatSSE error", e);
        }
    }

    private OkHttpClient buildOkHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    private void streamCompletionsDealing(String url, String data, Headers headers, OkHttpClient okHttpClient,
                                         EventSourceListener eventSourceListener) {
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), data);
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .headers(headers)
                    .post(requestBody)
                    .build();
            
            EventSource.Factory factory = EventSources.createFactory(okHttpClient);
            factory.newEventSource(request, eventSourceListener);
        } catch (Exception e) {
            log.error("streamCompletionsDealing error | data:{}", data, e);
        }
    }
}