package org.rc.platform.testagent.chat.entity;

import java.util.Date;


import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * AI用户评价结果(ChatComment)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@Data
@TableName(value = "ai_chat_comment")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class ChatComment extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private Long conversationId;
    /**
     * 会话内容
     */
    @Schema(description = "会话内容")
    private Long contentId;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private String modelType;
    /**
     * 是否喜欢，默认喜欢1
     */
    @Schema(description = "是否喜欢，默认喜欢1")
    private Integer isLike;
    /**
     * 默认评论
     */
    @Schema(description = "默认评论")
    private String defaultComment;
    /**
     * 是否采纳，默认否0
     */
    @Schema(description = "是否采纳，默认否0")
    private Integer isAccept;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

}
