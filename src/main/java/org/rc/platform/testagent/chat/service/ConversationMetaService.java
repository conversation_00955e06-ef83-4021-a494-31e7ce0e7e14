package org.rc.platform.testagent.chat.service;


import org.rc.platform.testagent.chat.entity.ConversationMeta;
import org.rc.platform.testagent.chat.vo.ConversationMetaVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 对话元数据表(ConversationMeta)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
public interface ConversationMetaService extends BaseService<ConversationMeta> {

    /**
     * 自定义分页
     *
     * @param page
     * @param conversationMeta
     * @return
     */
    IPage<ConversationMetaVO> selectConversationMetaPage(IPage<ConversationMetaVO> page, ConversationMetaVO conversationMeta);
}
