package org.rc.platform.testagent.chat.vo;


import org.rc.platform.testagent.chat.entity.ChatConversation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI会话信息表(ChatConversation)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI会话信息表")
public class ChatConversationVO extends ChatConversation {
    private static final long serialVersionUID = 1L;

}

