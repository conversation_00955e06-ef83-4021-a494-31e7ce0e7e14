package org.rc.platform.testagent.chat.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.chat.entity.ChatComment;

import org.rc.platform.testagent.chat.vo.ChatCommentVO;
import org.rc.platform.testagent.chat.wrapper.ChatCommentWrapper;


import org.rc.platform.testagent.chat.service.ChatCommentService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * AI用户评价结果(ChatComment)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
@RestController
@RequestMapping("/chatComment")
@Tag(name = "ChatComment", description = "AI用户评价结果接口")
public class ChatCommentController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ChatCommentService chatCommentService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入chatComment")
    public R<ChatCommentVO> detail(ChatComment chatComment) {
        ChatComment detail = chatCommentService.getOne(Condition.getQueryWrapper(chatComment));
        return R.data(ChatCommentWrapper.build().entityVO(detail));
    }

    /**
     * 分页 chatComment
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入chatComment")
    public R<IPage<ChatCommentVO>> list(ChatComment chatComment, Query query) {
        IPage<ChatComment> pages = chatCommentService.page(Condition.getPage(query), Condition.getQueryWrapper(chatComment));
        return R.data(ChatCommentWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 chatComment
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入chatComment")
    public R<IPage<ChatCommentVO>> page(ChatCommentVO chatComment, Query query) {
        IPage<ChatCommentVO> pages = chatCommentService.selectChatCommentPage(Condition.getPage(query), chatComment);
        return R.data(pages);
    }

    /**
     * 新增 chatComment
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入chatComment")
    public R save(@Valid @RequestBody ChatComment chatComment) {
        return R.status(chatCommentService.save(chatComment));
    }

    /**
     * 修改 chatComment
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入chatComment")
    public R update(@Valid @RequestBody ChatComment chatComment) {
        return R.status(chatCommentService.updateById(chatComment));
    }

    /**
     * 新增或修改 chatComment
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入chatComment")
    public R submit(@Valid @RequestBody ChatComment chatComment) {
        return R.status(chatCommentService.saveOrUpdate(chatComment));
    }


    /**
     * 删除 chatComment
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(chatCommentService.deleteLogic(Func.toLongList(ids)));
    }


}
