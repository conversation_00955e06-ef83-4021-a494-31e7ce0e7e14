package org.rc.platform.testagent.chat.service;


import org.rc.platform.testagent.chat.entity.ChatContent;
import org.rc.platform.testagent.chat.vo.ChatContentVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI会话内容表(ChatContent)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:37
 */
public interface ChatContentService extends BaseService<ChatContent> {

    /**
     * 自定义分页
     *
     * @param page
     * @param chatContent
     * @return
     */
    IPage<ChatContentVO> selectChatContentPage(IPage<ChatContentVO> page, ChatContentVO chatContent);
}
