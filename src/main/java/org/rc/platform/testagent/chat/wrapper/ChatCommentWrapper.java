/**
 * richinfo
 */


package org.rc.platform.testagent.chat.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.chat.entity.ChatComment;
import org.rc.platform.testagent.chat.vo.ChatCommentVO;


/**
 * AI用户评价结果(ChatComment)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:09:38
 */
public class ChatCommentWrapper extends BaseEntityWrapper<ChatComment, ChatCommentVO> {

    public static ChatCommentWrapper build() {
        return new ChatCommentWrapper();
    }

    @Override
    public ChatCommentVO entityVO(ChatComment chatComment) {
        ChatCommentVO chatCommentVO = BeanUtil.copyProperties(chatComment, ChatCommentVO.class);

        return chatCommentVO;
    }

}

