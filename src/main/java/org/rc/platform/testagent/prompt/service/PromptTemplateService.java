package org.rc.platform.testagent.prompt.service;


import org.rc.platform.testagent.prompt.entity.PromptTemplate;
import org.rc.platform.testagent.prompt.vo.PromptTemplateVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 提示词模版(PromptTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:28
 */
public interface PromptTemplateService extends BaseService<PromptTemplate> {

    /**
     * 自定义分页
     *
     * @param page
     * @param promptTemplate
     * @return
     */
    IPage<PromptTemplateVO> selectPromptTemplatePage(IPage<PromptTemplateVO> page, PromptTemplateVO promptTemplate);
}
