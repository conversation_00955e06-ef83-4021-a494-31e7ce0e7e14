package org.rc.platform.testagent.prompt.service.impl;


import org.rc.platform.testagent.prompt.entity.PromptVariable;
import org.rc.platform.testagent.prompt.mapper.PromptVariableMapper;
import org.rc.platform.testagent.prompt.vo.PromptVariableVO;
import org.rc.platform.testagent.prompt.service.PromptVariableService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 提示词变量(PromptVariable)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:29
 */
@Service
public class PromptVariableServiceImpl extends BaseServiceImpl<PromptVariableMapper, PromptVariable> implements PromptVariableService {

    @Override
    public IPage<PromptVariableVO> selectPromptVariablePage(IPage<PromptVariableVO> page, PromptVariableVO promptVariable) {
        return page.setRecords(baseMapper.selectPromptVariablePage(page, promptVariable));
    }

}
