package org.rc.platform.testagent.prompt.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.prompt.entity.PromptVariable;

import org.rc.platform.testagent.prompt.vo.PromptVariableVO;
import org.rc.platform.testagent.prompt.wrapper.PromptVariableWrapper;


import org.rc.platform.testagent.prompt.service.PromptVariableService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 提示词变量(PromptVariable)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:29
 */
@RestController
@RequestMapping("/promptVariable")
@Tag(name = "PromptVariable", description = "提示词变量接口")
public class PromptVariableController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private PromptVariableService promptVariableService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入promptVariable")
    public R<PromptVariableVO> detail(PromptVariable promptVariable) {
        PromptVariable detail = promptVariableService.getOne(Condition.getQueryWrapper(promptVariable));
        return R.data(PromptVariableWrapper.build().entityVO(detail));
    }

    /**
     * 分页 promptVariable
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入promptVariable")
    public R<IPage<PromptVariableVO>> list(PromptVariable promptVariable, Query query) {
        IPage<PromptVariable> pages = promptVariableService.page(Condition.getPage(query), Condition.getQueryWrapper(promptVariable));
        return R.data(PromptVariableWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 promptVariable
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入promptVariable")
    public R<IPage<PromptVariableVO>> page(PromptVariableVO promptVariable, Query query) {
        IPage<PromptVariableVO> pages = promptVariableService.selectPromptVariablePage(Condition.getPage(query), promptVariable);
        return R.data(pages);
    }

    /**
     * 新增 promptVariable
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入promptVariable")
    public R save(@Valid @RequestBody PromptVariable promptVariable) {
        return R.status(promptVariableService.save(promptVariable));
    }

    /**
     * 修改 promptVariable
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入promptVariable")
    public R update(@Valid @RequestBody PromptVariable promptVariable) {
        return R.status(promptVariableService.updateById(promptVariable));
    }

    /**
     * 新增或修改 promptVariable
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入promptVariable")
    public R submit(@Valid @RequestBody PromptVariable promptVariable) {
        return R.status(promptVariableService.saveOrUpdate(promptVariable));
    }


    /**
     * 删除 promptVariable
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(promptVariableService.deleteLogic(Func.toLongList(ids)));
    }


}
