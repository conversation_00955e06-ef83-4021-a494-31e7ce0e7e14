/**
 * richinfo
 */


package org.rc.platform.testagent.prompt.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.prompt.entity.PromptVariable;
import org.rc.platform.testagent.prompt.vo.PromptVariableVO;


/**
 * 提示词变量(PromptVariable)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:29
 */
public class PromptVariableWrapper extends BaseEntityWrapper<PromptVariable, PromptVariableVO> {

    public static PromptVariableWrapper build() {
        return new PromptVariableWrapper();
    }

    @Override
    public PromptVariableVO entityVO(PromptVariable promptVariable) {
        PromptVariableVO promptVariableVO = BeanUtil.copyProperties(promptVariable, PromptVariableVO.class);

        return promptVariableVO;
    }

}

