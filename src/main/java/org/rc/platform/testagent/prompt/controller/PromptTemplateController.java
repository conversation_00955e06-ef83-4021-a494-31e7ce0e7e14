package org.rc.platform.testagent.prompt.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.prompt.entity.PromptTemplate;

import org.rc.platform.testagent.prompt.vo.PromptTemplateVO;
import org.rc.platform.testagent.prompt.wrapper.PromptTemplateWrapper;


import org.rc.platform.testagent.prompt.service.PromptTemplateService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 提示词模版(PromptTemplate)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:28
 */
@RestController
@RequestMapping("/promptTemplate")
@Tag(name = "PromptTemplate", description = "提示词模版接口")
public class PromptTemplateController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private PromptTemplateService promptTemplateService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入promptTemplate")
    public R<PromptTemplateVO> detail(PromptTemplate promptTemplate) {
        PromptTemplate detail = promptTemplateService.getOne(Condition.getQueryWrapper(promptTemplate));
        return R.data(PromptTemplateWrapper.build().entityVO(detail));
    }

    /**
     * 分页 promptTemplate
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入promptTemplate")
    public R<IPage<PromptTemplateVO>> list(PromptTemplate promptTemplate, Query query) {
        IPage<PromptTemplate> pages = promptTemplateService.page(Condition.getPage(query), Condition.getQueryWrapper(promptTemplate));
        return R.data(PromptTemplateWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 promptTemplate
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入promptTemplate")
    public R<IPage<PromptTemplateVO>> page(PromptTemplateVO promptTemplate, Query query) {
        IPage<PromptTemplateVO> pages = promptTemplateService.selectPromptTemplatePage(Condition.getPage(query), promptTemplate);
        return R.data(pages);
    }

    /**
     * 新增 promptTemplate
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入promptTemplate")
    public R save(@Valid @RequestBody PromptTemplate promptTemplate) {
        return R.status(promptTemplateService.save(promptTemplate));
    }

    /**
     * 修改 promptTemplate
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入promptTemplate")
    public R update(@Valid @RequestBody PromptTemplate promptTemplate) {
        return R.status(promptTemplateService.updateById(promptTemplate));
    }

    /**
     * 新增或修改 promptTemplate
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入promptTemplate")
    public R submit(@Valid @RequestBody PromptTemplate promptTemplate) {
        return R.status(promptTemplateService.saveOrUpdate(promptTemplate));
    }


    /**
     * 删除 promptTemplate
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(promptTemplateService.deleteLogic(Func.toLongList(ids)));
    }


}
