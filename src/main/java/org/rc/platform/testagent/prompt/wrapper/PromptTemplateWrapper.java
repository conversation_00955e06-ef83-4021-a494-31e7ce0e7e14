/**
 * richinfo
 */


package org.rc.platform.testagent.prompt.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.prompt.entity.PromptTemplate;
import org.rc.platform.testagent.prompt.vo.PromptTemplateVO;


/**
 * 提示词模版(PromptTemplate)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:28
 */
public class PromptTemplateWrapper extends BaseEntityWrapper<PromptTemplate, PromptTemplateVO> {

    public static PromptTemplateWrapper build() {
        return new PromptTemplateWrapper();
    }

    @Override
    public PromptTemplateVO entityVO(PromptTemplate promptTemplate) {
        PromptTemplateVO promptTemplateVO = BeanUtil.copyProperties(promptTemplate, PromptTemplateVO.class);

        return promptTemplateVO;
    }

}

