package org.rc.platform.testagent.prompt.entity;

import java.util.Date;


import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * 提示词变量(PromptVariable)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:29
 */
@Data
@TableName(value = "ai_prompt_variable")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class PromptVariable extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 提示词模版
     */
    @Schema(description = "提示词模版")
    private Long promptTemplateId;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 类型
     */
    @Schema(description = "类型")
    private String type;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 默认值
     */
    @Schema(description = "默认值")
    private String defaultValue;
    /**
     * 是否必填
     */
    @Schema(description = "是否必填")
    private Integer isRequired;
    /**
     * 允许值-JSON格式
     */
    @Schema(description = "允许值-JSON格式")
    private String allowedValues;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

}
