<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.prompt.mapper.PromptTemplateMapper">

    <resultMap type="org.rc.platform.testagent.prompt.entity.PromptTemplate" id="promptTemplateResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tplName" column="tpl_name" jdbcType="VARCHAR"/>
        <result property="tplDescription" column="tpl_description" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="author" column="author" jdbcType="INTEGER"/>
        <result property="metadata" column="metadata" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_prompt_template(tpl_name, tpl_description, content, author, metadata, create_time,
        update_time, tenant_id, version, create_user, update_user, is_deleted, status, parent_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.tplName}, #{entity.tplDescription}, #{entity.content}, #{entity.author}, #{entity.metadata},
            #{entity.createTime}, #{entity.updateTime}, #{entity.tenantId}, #{entity.version}, #{entity.createUser},
            #{entity.updateUser}, #{entity.isDeleted}, #{entity.status}, #{entity.parentId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_prompt_template(tpl_name, tpl_description, content, author, metadata, create_time,
        update_time, tenant_id, version, create_user, update_user, is_deleted, status, parent_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.tplName}, #{entity.tplDescription}, #{entity.content}, #{entity.author}, #{entity.metadata},
            #{entity.createTime}, #{entity.updateTime}, #{entity.tenantId}, #{entity.version}, #{entity.createUser},
            #{entity.updateUser}, #{entity.isDeleted}, #{entity.status}, #{entity.parentId})
        </foreach>
        on duplicate key update
        tpl_name = values(tpl_name) , tpl_description = values(tpl_description) , content = values(content) , author =
        values(author) , metadata = values(metadata) , create_time = values(create_time) , update_time =
        values(update_time) , tenant_id = values(tenant_id) , version = values(version) , create_user =
        values(create_user) , update_user = values(update_user) , is_deleted = values(is_deleted) , status =
        values(status) , parent_id = values(parent_id)
    </insert>
    <select id="selectPromptTemplatePage" resultMap="promptTemplateResultMap">
        select *
        from ai_prompt_template
        where is_deleted = 0
    </select>

</mapper>
