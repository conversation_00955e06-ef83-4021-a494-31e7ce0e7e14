<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.prompt.mapper.PromptVariableMapper">

    <resultMap type="org.rc.platform.testagent.prompt.entity.PromptVariable" id="promptVariableResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="promptTemplateId" column="prompt_template_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="defaultValue" column="default_value" jdbcType="VARCHAR"/>
        <result property="isRequired" column="is_required" jdbcType="INTEGER"/>
        <result property="allowedValues" column="allowed_values" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_prompt_variable(prompt_template_id, name, type, description, default_value, is_required,
        allowed_values, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.promptTemplateId}, #{entity.name}, #{entity.type}, #{entity.description}, #{entity.defaultValue},
            #{entity.isRequired}, #{entity.allowedValues}, #{entity.createTime}, #{entity.updateTime},
            #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser}, #{entity.isDeleted},
            #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_prompt_variable(prompt_template_id, name, type, description, default_value, is_required,
        allowed_values, create_time, update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.promptTemplateId}, #{entity.name}, #{entity.type}, #{entity.description}, #{entity.defaultValue},
            #{entity.isRequired}, #{entity.allowedValues}, #{entity.createTime}, #{entity.updateTime},
            #{entity.tenantId}, #{entity.version}, #{entity.createUser}, #{entity.updateUser}, #{entity.isDeleted},
            #{entity.status})
        </foreach>
        on duplicate key update
        prompt_template_id = values(prompt_template_id) , name = values(name) , type = values(type) , description =
        values(description) , default_value = values(default_value) , is_required = values(is_required) , allowed_values
        = values(allowed_values) , create_time = values(create_time) , update_time = values(update_time) , tenant_id =
        values(tenant_id) , version = values(version) , create_user = values(create_user) , update_user =
        values(update_user) , is_deleted = values(is_deleted) , status = values(status)
    </insert>
    <select id="selectPromptVariablePage" resultMap="promptVariableResultMap">
        select *
        from ai_prompt_variable
        where is_deleted = 0
    </select>

</mapper>
