package org.rc.platform.testagent.prompt.vo;


import org.rc.platform.testagent.prompt.entity.PromptVariable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 提示词变量(PromptVariable)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "提示词变量")
public class PromptVariableVO extends PromptVariable {
    private static final long serialVersionUID = 1L;

}

