package org.rc.platform.testagent.prompt.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.prompt.entity.PromptTemplate;

import org.rc.platform.testagent.prompt.vo.PromptTemplateVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 提示词模版(PromptTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:28
 */
public interface PromptTemplateMapper extends BaseMapper<PromptTemplate> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PromptTemplate> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PromptTemplate> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PromptTemplate> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PromptTemplate> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param promptTemplate
     * @return
     */
    List<PromptTemplateVO> selectPromptTemplatePage(IPage page, PromptTemplateVO promptTemplate);

}
