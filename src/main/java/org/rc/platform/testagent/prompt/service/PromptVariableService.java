package org.rc.platform.testagent.prompt.service;


import org.rc.platform.testagent.prompt.entity.PromptVariable;
import org.rc.platform.testagent.prompt.vo.PromptVariableVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 提示词变量(PromptVariable)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:29
 */
public interface PromptVariableService extends BaseService<PromptVariable> {

    /**
     * 自定义分页
     *
     * @param page
     * @param promptVariable
     * @return
     */
    IPage<PromptVariableVO> selectPromptVariablePage(IPage<PromptVariableVO> page, PromptVariableVO promptVariable);
}
