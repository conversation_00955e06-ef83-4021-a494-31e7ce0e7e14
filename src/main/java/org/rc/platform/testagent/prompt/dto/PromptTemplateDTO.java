package org.rc.platform.testagent.prompt.dto;


import org.rc.platform.testagent.prompt.entity.PromptTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 提示词模版(PromptTemplate)数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PromptTemplateDTO extends PromptTemplate {
    private static final long serialVersionUID = 1L;

}

