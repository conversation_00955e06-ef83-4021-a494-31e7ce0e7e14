package org.rc.platform.testagent.prompt.vo;


import org.rc.platform.testagent.prompt.entity.PromptTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 提示词模版(PromptTemplate)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "提示词模版")
public class PromptTemplateVO extends PromptTemplate {
    private static final long serialVersionUID = 1L;

}

