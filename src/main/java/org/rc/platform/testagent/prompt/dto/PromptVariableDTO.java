package org.rc.platform.testagent.prompt.dto;


import org.rc.platform.testagent.prompt.entity.PromptVariable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 提示词变量(PromptVariable)数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PromptVariableDTO extends PromptVariable {
    private static final long serialVersionUID = 1L;

}

