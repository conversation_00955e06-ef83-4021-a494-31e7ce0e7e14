package org.rc.platform.testagent.prompt.service.impl;


import org.rc.platform.testagent.prompt.entity.PromptTemplate;
import org.rc.platform.testagent.prompt.mapper.PromptTemplateMapper;
import org.rc.platform.testagent.prompt.vo.PromptTemplateVO;
import org.rc.platform.testagent.prompt.service.PromptTemplateService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 提示词模版(PromptTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:11:28
 */
@Service
public class PromptTemplateServiceImpl extends BaseServiceImpl<PromptTemplateMapper, PromptTemplate> implements PromptTemplateService {

    @Override
    public IPage<PromptTemplateVO> selectPromptTemplatePage(IPage<PromptTemplateVO> page, PromptTemplateVO promptTemplate) {
        return page.setRecords(baseMapper.selectPromptTemplatePage(page, promptTemplate));
    }

}
