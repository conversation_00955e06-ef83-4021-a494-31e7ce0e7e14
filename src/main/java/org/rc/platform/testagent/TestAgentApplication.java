package org.rc.platform.testagent;

import org.mybatis.spring.annotation.MapperScan;
import org.richinfo.framework.core.cloud.client.RichinfoCloudApplication;
import org.richinfo.framework.core.launch.RichinfoApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 系统模块启动器
 *
 * <AUTHOR>
 */
@RichinfoCloudApplication
@EnableConfigurationProperties
@MapperScan(basePackages = {
        "org.rc.platform.testagent.application.mapper",
        "org.rc.platform.testagent.chat.mapper",
        "org.rc.platform.testagent.memory.mapper",
        "org.rc.platform.testagent.prompt.mapper",
        "org.rc.platform.testagent.task.mapper"
})
public class TestAgentApplication {

    private static final Logger logger = LoggerFactory.getLogger(TestAgentApplication.class);

    public static void main(String[] args) {
        try {
            logger.info("Starting application with module name: testagent");
            // 添加调试日志，记录启动参数
            logger.debug("Application arguments: {}", (Object[]) args);
            RichinfoApplication.run("testagent", TestAgentApplication.class, args);
            logger.info("Application started successfully.");
        } catch (Exception e) {
            logger.error("Failed to start application. Module name: testagent, Exception message: {}", e.getMessage(), e);
            // 添加堆栈跟踪日志
            logger.error("Stack trace: ", e);
            // 抛出自定义异常，提供更清晰的错误信息
            throw new RuntimeException("Application startup failed. Please check the logs for details.", e);
        }
    }
}