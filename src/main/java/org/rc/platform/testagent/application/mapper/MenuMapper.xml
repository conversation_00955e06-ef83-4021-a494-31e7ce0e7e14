<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.application.mapper.MenuMapper">

    <resultMap type="org.rc.platform.testagent.application.entity.Menu" id="menuResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="labels" column="labels" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="action" column="action" jdbcType="VARCHAR"/>
        <result property="isOpen" column="is_open" jdbcType="VARCHAR"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="requestType" column="request_type" jdbcType="INTEGER"/>
        <result property="pageId" column="page_id" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="menuVersion" column="menu_version" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_menu(app_id, name, labels, icon, code, description, source, category, action, is_open,
        path, request_type, page_id, parent_id, level, sort, tenant_id, status, create_user, update_user, create_time,
        update_time, is_deleted, remark, menu_version, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.name}, #{entity.labels}, #{entity.icon}, #{entity.code}, #{entity.description},
            #{entity.source}, #{entity.category}, #{entity.action}, #{entity.isOpen}, #{entity.path},
            #{entity.requestType}, #{entity.pageId}, #{entity.parentId}, #{entity.level}, #{entity.sort},
            #{entity.tenantId}, #{entity.status}, #{entity.createUser}, #{entity.updateUser}, #{entity.createTime},
            #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark}, #{entity.menuVersion}, #{entity.version})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_menu(app_id, name, labels, icon, code, description, source, category, action, is_open,
        path, request_type, page_id, parent_id, level, sort, tenant_id, status, create_user, update_user, create_time,
        update_time, is_deleted, remark, menu_version, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.name}, #{entity.labels}, #{entity.icon}, #{entity.code}, #{entity.description},
            #{entity.source}, #{entity.category}, #{entity.action}, #{entity.isOpen}, #{entity.path},
            #{entity.requestType}, #{entity.pageId}, #{entity.parentId}, #{entity.level}, #{entity.sort},
            #{entity.tenantId}, #{entity.status}, #{entity.createUser}, #{entity.updateUser}, #{entity.createTime},
            #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark}, #{entity.menuVersion}, #{entity.version})
        </foreach>
        on duplicate key update
        app_id = values(app_id) , name = values(name) , labels = values(labels) , icon = values(icon) , code =
        values(code) , description = values(description) , source = values(source) , category = values(category) ,
        action = values(action) , is_open = values(is_open) , path = values(path) , request_type = values(request_type)
        , page_id = values(page_id) , parent_id = values(parent_id) , level = values(level) , sort = values(sort) ,
        tenant_id = values(tenant_id) , status = values(status) , create_user = values(create_user) , update_user =
        values(update_user) , create_time = values(create_time) , update_time = values(update_time) , is_deleted =
        values(is_deleted) , remark = values(remark) , menu_version = values(menu_version) , version = values(version)
    </insert>
    <select id="selectMenuPage" resultMap="menuResultMap">
        select *
        from ai_menu
        where is_deleted = 0
    </select>

</mapper>
