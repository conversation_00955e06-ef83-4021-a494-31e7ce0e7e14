/**
 * richinfo
 */


package org.rc.platform.testagent.application.wrapper;


import org.rc.platform.testagent.application.entity.Module;
import org.rc.platform.testagent.application.vo.ModuleVO;
import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;


/**
 * 模块(Module)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:39
 */
public class ModuleWrapper extends BaseEntityWrapper<Module, ModuleVO> {

    public static ModuleWrapper build() {
        return new ModuleWrapper();
    }

    @Override
    public ModuleVO entityVO(Module module) {
        ModuleVO moduleVO = BeanUtil.copyProperties(module, ModuleVO.class);

        return moduleVO;
    }

}

