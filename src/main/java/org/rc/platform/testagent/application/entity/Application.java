package org.rc.platform.testagent.application.entity;

import java.util.Date;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * 应用(Application)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:41
 */
@Data
@TableName(value = "ai_application")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class Application extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 环境ID
     */
    @Schema(description = "环境ID")
    private Long envId;
    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 平台-PC1|H5 2|Android 3|IOS 4|鸿蒙5
     */
    @Schema(description = "平台-PC1|H5 2|Android 3|IOS 4|鸿蒙5")
    private String platform;
    /**
     * 应用分类
     */
    @Schema(description = "应用分类")
    private String appCategory;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 应用图标
     */
    @Schema(description = "应用图标")
    private String appIcon;
    /**
     * 1信息技术|2制造业|3零售业|4服务业|5信息技术|6医疗行业|7教育行业|8建筑业|9农业
     */
    @Schema(description = "1信息技术|2制造业|3零售业|4服务业|5信息技术|6医疗行业|7教育行业|8建筑业|9农业")
    private Integer industry;
    /**
     * 业务功能-1运营管理|2财务管理|3市场营销|4人力资源|5客户服务
     */
    @Schema(description = "业务功能-1运营管理|2财务管理|3市场营销|4人力资源|5客户服务")
    private Integer business;
    /**
     * 应用类型-1Web应用|2移动应用|3桌面应用|4集成应用
     */
    @Schema(description = "应用类型-1Web应用|2移动应用|3桌面应用|4集成应用")
    private Integer appType;
    /**
     * 发布版本
     */
    @Schema(description = "发布版本")
    private String releaseVersion;
    /**
     * 应用版本
     */
    @Schema(description = "应用版本")
    private Integer appVersion;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;

}
