package org.rc.platform.testagent.application.vo;


import org.rc.platform.testagent.application.entity.Env;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 应用环境(Env)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用环境")
public class EnvVO extends Env {
    private static final long serialVersionUID = 1L;

}

