package org.rc.platform.testagent.application.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.validation.Valid;

import org.rc.platform.testagent.application.entity.Env;
import org.rc.platform.testagent.application.service.EnvService;
import org.rc.platform.testagent.application.vo.EnvVO;
import org.rc.platform.testagent.application.wrapper.EnvWrapper;
import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 应用环境(Env)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:40
 */
@RestController
@RequestMapping("/env")
@Tag(name = "Env", description = "应用环境接口")
public class EnvController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private EnvService envService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入env")
    public R<EnvVO> detail(Env env) {
        Env detail = envService.getOne(Condition.getQueryWrapper(env));
        return R.data(EnvWrapper.build().entityVO(detail));
    }

    /**
     * 分页 env
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入env")
    public R<IPage<EnvVO>> list(Env env, Query query) {
        IPage<Env> pages = envService.page(Condition.getPage(query), Condition.getQueryWrapper(env));
        return R.data(EnvWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 env
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入env")
    public R<IPage<EnvVO>> page(EnvVO env, Query query) {
        IPage<EnvVO> pages = envService.selectEnvPage(Condition.getPage(query), env);
        return R.data(pages);
    }

    /**
     * 新增 env
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入env")
    public R save(@Valid @RequestBody Env env) {
        return R.status(envService.save(env));
    }

    /**
     * 修改 env
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入env")
    public R update(@Valid @RequestBody Env env) {
        return R.status(envService.updateById(env));
    }

    /**
     * 新增或修改 env
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入env")
    public R submit(@Valid @RequestBody Env env) {
        return R.status(envService.saveOrUpdate(env));
    }


    /**
     * 删除 env
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(envService.deleteLogic(Func.toLongList(ids)));
    }


}
