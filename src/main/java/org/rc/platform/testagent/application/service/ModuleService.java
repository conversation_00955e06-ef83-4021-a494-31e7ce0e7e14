package org.rc.platform.testagent.application.service;


import org.rc.platform.testagent.application.entity.Module;
import org.rc.platform.testagent.application.vo.ModuleVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 模块(Module)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:39
 */
public interface ModuleService extends BaseService<Module> {

    /**
     * 自定义分页
     *
     * @param page
     * @param module
     * @return
     */
    IPage<ModuleVO> selectModulePage(IPage<ModuleVO> page, ModuleVO module);
}
