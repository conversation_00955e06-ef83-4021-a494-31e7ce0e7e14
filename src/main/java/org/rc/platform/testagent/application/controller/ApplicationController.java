package org.rc.platform.testagent.application.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.validation.Valid;

import org.rc.platform.testagent.application.entity.Application;
import org.rc.platform.testagent.application.service.ApplicationService;
import org.rc.platform.testagent.application.vo.ApplicationVO;
import org.rc.platform.testagent.application.wrapper.ApplicationWrapper;
import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 应用(Application)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:41
 */
@RestController
@RequestMapping("/application")
@Tag(name = "Application", description = "应用接口")
public class ApplicationController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ApplicationService applicationService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入application")
    public R<ApplicationVO> detail(Application application) {
        Application detail = applicationService.getOne(Condition.getQueryWrapper(application));
        return R.data(ApplicationWrapper.build().entityVO(detail));
    }

    /**
     * 分页 application
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入application")
    public R<IPage<ApplicationVO>> list(Application application, Query query) {
        IPage<Application> pages = applicationService.page(Condition.getPage(query), Condition.getQueryWrapper(application));
        return R.data(ApplicationWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 application
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入application")
    public R<IPage<ApplicationVO>> page(ApplicationVO application, Query query) {
        IPage<ApplicationVO> pages = applicationService.selectApplicationPage(Condition.getPage(query), application);
        return R.data(pages);
    }

    /**
     * 新增 application
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入application")
    public R save(@Valid @RequestBody Application application) {
        return R.status(applicationService.save(application));
    }

    /**
     * 修改 application
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入application")
    public R update(@Valid @RequestBody Application application) {
        return R.status(applicationService.updateById(application));
    }

    /**
     * 新增或修改 application
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入application")
    public R submit(@Valid @RequestBody Application application) {
        return R.status(applicationService.saveOrUpdate(application));
    }


    /**
     * 删除 application
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(applicationService.deleteLogic(Func.toLongList(ids)));
    }


}
