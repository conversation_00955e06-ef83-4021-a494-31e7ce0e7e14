/**
 * richinfo
 */


package org.rc.platform.testagent.application.wrapper;


import org.rc.platform.testagent.application.entity.Menu;
import org.rc.platform.testagent.application.vo.MenuVO;
import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;


/**
 * 菜单(Menu)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:38
 */
public class MenuWrapper extends BaseEntityWrapper<Menu, MenuVO> {

    public static MenuWrapper build() {
        return new MenuWrapper();
    }

    @Override
    public MenuVO entityVO(Menu menu) {
        MenuVO menuVO = BeanUtil.copyProperties(menu, MenuVO.class);

        return menuVO;
    }

}

