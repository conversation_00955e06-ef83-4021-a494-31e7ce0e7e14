/**
 * richinfo
 */


package org.rc.platform.testagent.application.wrapper;


import org.rc.platform.testagent.application.entity.Application;
import org.rc.platform.testagent.application.vo.ApplicationVO;
import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;


/**
 * 应用(Application)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:42
 */
public class ApplicationWrapper extends BaseEntityWrapper<Application, ApplicationVO> {

    public static ApplicationWrapper build() {
        return new ApplicationWrapper();
    }

    @Override
    public ApplicationVO entityVO(Application application) {
        ApplicationVO applicationVO = BeanUtil.copyProperties(application, ApplicationVO.class);

        return applicationVO;
    }

}

