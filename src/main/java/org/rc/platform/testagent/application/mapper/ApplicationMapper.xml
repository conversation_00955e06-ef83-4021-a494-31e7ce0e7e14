<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.application.mapper.ApplicationMapper">

    <resultMap type="org.rc.platform.testagent.application.entity.Application" id="applicationResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="envId" column="env_id" jdbcType="INTEGER"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="platform" column="platform" jdbcType="VARCHAR"/>
        <result property="appCategory" column="app_category" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="appIcon" column="app_icon" jdbcType="VARCHAR"/>
        <result property="industry" column="industry" jdbcType="INTEGER"/>
        <result property="business" column="business" jdbcType="INTEGER"/>
        <result property="appType" column="app_type" jdbcType="INTEGER"/>
        <result property="releaseVersion" column="release_version" jdbcType="VARCHAR"/>
        <result property="appVersion" column="app_version" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_application(env_id, code, name, platform, app_category, description, app_icon, industry,
        business, app_type, release_version, app_version, status, create_user, update_user, create_time, update_time,
        is_deleted, remark, sort, tenant_id, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.envId}, #{entity.code}, #{entity.name}, #{entity.platform}, #{entity.appCategory},
            #{entity.description}, #{entity.appIcon}, #{entity.industry}, #{entity.business}, #{entity.appType},
            #{entity.releaseVersion}, #{entity.appVersion}, #{entity.status}, #{entity.createUser},
            #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark},
            #{entity.sort}, #{entity.tenantId}, #{entity.version})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_application(env_id, code, name, platform, app_category, description, app_icon, industry,
        business, app_type, release_version, app_version, status, create_user, update_user, create_time, update_time,
        is_deleted, remark, sort, tenant_id, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.envId}, #{entity.code}, #{entity.name}, #{entity.platform}, #{entity.appCategory},
            #{entity.description}, #{entity.appIcon}, #{entity.industry}, #{entity.business}, #{entity.appType},
            #{entity.releaseVersion}, #{entity.appVersion}, #{entity.status}, #{entity.createUser},
            #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark},
            #{entity.sort}, #{entity.tenantId}, #{entity.version})
        </foreach>
        on duplicate key update
        env_id = values(env_id) , code = values(code) , name = values(name) , platform = values(platform) , app_category
        = values(app_category) , description = values(description) , app_icon = values(app_icon) , industry =
        values(industry) , business = values(business) , app_type = values(app_type) , release_version =
        values(release_version) , app_version = values(app_version) , status = values(status) , create_user =
        values(create_user) , update_user = values(update_user) , create_time = values(create_time) , update_time =
        values(update_time) , is_deleted = values(is_deleted) , remark = values(remark) , sort = values(sort) ,
        tenant_id = values(tenant_id) , version = values(version)
    </insert>
    <select id="selectApplicationPage" resultMap="applicationResultMap">
        select *
        from ai_application
        where is_deleted = 0
    </select>

</mapper>
