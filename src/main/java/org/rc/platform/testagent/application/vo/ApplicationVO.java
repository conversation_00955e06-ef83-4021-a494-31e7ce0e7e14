package org.rc.platform.testagent.application.vo;


import org.rc.platform.testagent.application.entity.Application;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 应用(Application)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用")
public class ApplicationVO extends Application {
    private static final long serialVersionUID = 1L;

}

