package org.rc.platform.testagent.application.service;


import org.rc.platform.testagent.application.entity.Menu;
import org.rc.platform.testagent.application.vo.MenuVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 菜单(Menu)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:38
 */
public interface MenuService extends BaseService<Menu> {

    /**
     * 自定义分页
     *
     * @param page
     * @param menu
     * @return
     */
    IPage<MenuVO> selectMenuPage(IPage<MenuVO> page, MenuVO menu);
}
