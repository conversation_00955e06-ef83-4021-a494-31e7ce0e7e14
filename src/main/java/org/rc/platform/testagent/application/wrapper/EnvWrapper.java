/**
 * richinfo
 */


package org.rc.platform.testagent.application.wrapper;


import org.rc.platform.testagent.application.entity.Env;
import org.rc.platform.testagent.application.vo.EnvVO;
import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;


/**
 * 应用环境(Env)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:41
 */
public class EnvWrapper extends BaseEntityWrapper<Env, EnvVO> {

    public static EnvWrapper build() {
        return new EnvWrapper();
    }

    @Override
    public EnvVO entityVO(Env env) {
        EnvVO envVO = BeanUtil.copyProperties(env, EnvVO.class);

        return envVO;
    }

}

