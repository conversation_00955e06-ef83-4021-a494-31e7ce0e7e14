<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.application.mapper.ModuleMapper">

    <resultMap type="org.rc.platform.testagent.application.entity.Module" id="moduleResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="moduleType" column="module_type" jdbcType="INTEGER"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_module(app_id, name, module_type, description, status, create_user, update_user,
        create_time, update_time, is_deleted, remark, sort, tenant_id, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.name}, #{entity.moduleType}, #{entity.description}, #{entity.status},
            #{entity.createUser}, #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted},
            #{entity.remark}, #{entity.sort}, #{entity.tenantId}, #{entity.version})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_module(app_id, name, module_type, description, status, create_user, update_user,
        create_time, update_time, is_deleted, remark, sort, tenant_id, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.name}, #{entity.moduleType}, #{entity.description}, #{entity.status},
            #{entity.createUser}, #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted},
            #{entity.remark}, #{entity.sort}, #{entity.tenantId}, #{entity.version})
        </foreach>
        on duplicate key update
        app_id = values(app_id) , name = values(name) , module_type = values(module_type) , description =
        values(description) , status = values(status) , create_user = values(create_user) , update_user =
        values(update_user) , create_time = values(create_time) , update_time = values(update_time) , is_deleted =
        values(is_deleted) , remark = values(remark) , sort = values(sort) , tenant_id = values(tenant_id) , version =
        values(version)
    </insert>
    <select id="selectModulePage" resultMap="moduleResultMap">
        select *
        from ai_module
        where is_deleted = 0
    </select>

</mapper>
