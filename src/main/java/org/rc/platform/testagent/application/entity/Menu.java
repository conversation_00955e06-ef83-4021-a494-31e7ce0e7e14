package org.rc.platform.testagent.application.entity;

import java.util.Date;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * 菜单(Menu)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:38
 */
@Data
@TableName(value = "ai_menu")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class Menu extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private Long appId;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 标签
     */
    @Schema(description = "标签")
    private String labels;
    /**
     * 图标
     */
    @Schema(description = "图标")
    private String icon;
    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 菜单资源
     */
    @Schema(description = "菜单资源")
    private String source;
    /**
     * 菜单类型
     */
    @Schema(description = "菜单类型")
    private String category;
    /**
     * 按钮类型
     */
    @Schema(description = "按钮类型")
    private String action;
    /**
     * 是否打开新页面
     */
    @Schema(description = "是否打开新页面")
    private String isOpen;
    /**
     * 请求地址
     */
    @Schema(description = "请求地址")
    private String path;
    /**
     * 请求类型-0内部|1外部
     */
    @Schema(description = "请求类型-0内部|1外部")
    private Integer requestType;
    /**
     * 菜单页面id
     */
    @Schema(description = "菜单页面id")
    private Long pageId;
    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Long parentId;
    /**
     * 层级
     */
    @Schema(description = "层级")
    private Integer level;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
    /**
     * 菜单版本
     */
    @Schema(description = "菜单版本")
    private Integer menuVersion;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;

}
