package org.rc.platform.testagent.application.service.impl;


import org.rc.platform.testagent.application.entity.Application;
import org.rc.platform.testagent.application.service.ApplicationService;
import org.rc.platform.testagent.application.mapper.ApplicationMapper;
import org.rc.platform.testagent.application.vo.ApplicationVO;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 应用(Application)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:42
 */
@Service
public class ApplicationServiceImpl extends BaseServiceImpl<ApplicationMapper, Application> implements ApplicationService {

    @Override
    public IPage<ApplicationVO> selectApplicationPage(IPage<ApplicationVO> page, ApplicationVO application) {
        return page.setRecords(baseMapper.selectApplicationPage(page, application));
    }

}
