package org.rc.platform.testagent.application.service.impl;


import org.rc.platform.testagent.application.entity.Module;
import org.rc.platform.testagent.application.service.ModuleService;
import org.rc.platform.testagent.application.mapper.ModuleMapper;
import org.rc.platform.testagent.application.vo.ModuleVO;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 模块(Module)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:39
 */
@Service
public class ModuleServiceImpl extends BaseServiceImpl<ModuleMapper, Module> implements ModuleService {

    @Override
    public IPage<ModuleVO> selectModulePage(IPage<ModuleVO> page, ModuleVO module) {
        return page.setRecords(baseMapper.selectModulePage(page, module));
    }

}
