package org.rc.platform.testagent.application.service;


import org.rc.platform.testagent.application.entity.Application;
import org.rc.platform.testagent.application.vo.ApplicationVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 应用(Application)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:42
 */
public interface ApplicationService extends BaseService<Application> {

    /**
     * 自定义分页
     *
     * @param page
     * @param application
     * @return
     */
    IPage<ApplicationVO> selectApplicationPage(IPage<ApplicationVO> page, ApplicationVO application);
}
