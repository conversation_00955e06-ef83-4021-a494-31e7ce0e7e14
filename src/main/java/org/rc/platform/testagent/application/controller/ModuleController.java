package org.rc.platform.testagent.application.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.validation.Valid;

import org.rc.platform.testagent.application.entity.Module;
import org.rc.platform.testagent.application.service.ModuleService;
import org.rc.platform.testagent.application.vo.ModuleVO;
import org.rc.platform.testagent.application.wrapper.ModuleWrapper;
import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 模块(Module)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:39
 */
@RestController
@RequestMapping("/module")
@Tag(name = "Module", description = "模块接口")
public class ModuleController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ModuleService moduleService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入module")
    public R<ModuleVO> detail(Module module) {
        Module detail = moduleService.getOne(Condition.getQueryWrapper(module));
        return R.data(ModuleWrapper.build().entityVO(detail));
    }

    /**
     * 分页 module
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入module")
    public R<IPage<ModuleVO>> list(Module module, Query query) {
        IPage<Module> pages = moduleService.page(Condition.getPage(query), Condition.getQueryWrapper(module));
        return R.data(ModuleWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 module
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入module")
    public R<IPage<ModuleVO>> page(ModuleVO module, Query query) {
        IPage<ModuleVO> pages = moduleService.selectModulePage(Condition.getPage(query), module);
        return R.data(pages);
    }

    /**
     * 新增 module
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入module")
    public R save(@Valid @RequestBody Module module) {
        return R.status(moduleService.save(module));
    }

    /**
     * 修改 module
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入module")
    public R update(@Valid @RequestBody Module module) {
        return R.status(moduleService.updateById(module));
    }

    /**
     * 新增或修改 module
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入module")
    public R submit(@Valid @RequestBody Module module) {
        return R.status(moduleService.saveOrUpdate(module));
    }


    /**
     * 删除 module
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(moduleService.deleteLogic(Func.toLongList(ids)));
    }


}
