package org.rc.platform.testagent.application.service.impl;


import org.rc.platform.testagent.application.entity.Menu;
import org.rc.platform.testagent.application.service.MenuService;
import org.rc.platform.testagent.application.mapper.MenuMapper;
import org.rc.platform.testagent.application.vo.MenuVO;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 菜单(Menu)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:38
 */
@Service
public class MenuServiceImpl extends BaseServiceImpl<MenuMapper, Menu> implements MenuService {

    @Override
    public IPage<MenuVO> selectMenuPage(IPage<MenuVO> page, MenuVO menu) {
        return page.setRecords(baseMapper.selectMenuPage(page, menu));
    }

}
