package org.rc.platform.testagent.application.vo;


import org.rc.platform.testagent.application.entity.Menu;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 菜单(Menu)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "菜单")
public class MenuVO extends Menu {
    private static final long serialVersionUID = 1L;

}

