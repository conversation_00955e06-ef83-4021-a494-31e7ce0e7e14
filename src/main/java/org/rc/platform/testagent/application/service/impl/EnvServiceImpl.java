package org.rc.platform.testagent.application.service.impl;


import org.rc.platform.testagent.application.entity.Env;
import org.rc.platform.testagent.application.service.EnvService;
import org.rc.platform.testagent.application.mapper.EnvMapper;
import org.rc.platform.testagent.application.vo.EnvVO;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 应用环境(Env)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:41
 */
@Service
public class EnvServiceImpl extends BaseServiceImpl<EnvMapper, Env> implements EnvService {

    @Override
    public IPage<EnvVO> selectEnvPage(IPage<EnvVO> page, EnvVO env) {
        return page.setRecords(baseMapper.selectEnvPage(page, env));
    }

}
