package org.rc.platform.testagent.application.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.application.entity.Application;

import org.rc.platform.testagent.application.vo.ApplicationVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 应用(Application)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:42
 */
public interface ApplicationMapper extends BaseMapper<Application> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<Application> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<Application> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<Application> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<Application> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param application
     * @return
     */
    List<ApplicationVO> selectApplicationPage(IPage page, ApplicationVO application);

}
