package org.rc.platform.testagent.application.vo;


import org.rc.platform.testagent.application.entity.Module;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 模块(Module)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "模块")
public class ModuleVO extends Module {
    private static final long serialVersionUID = 1L;

}

