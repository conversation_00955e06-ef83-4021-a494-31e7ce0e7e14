package org.rc.platform.testagent.application.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.validation.Valid;

import org.rc.platform.testagent.application.entity.Menu;
import org.rc.platform.testagent.application.service.MenuService;
import org.rc.platform.testagent.application.vo.MenuVO;
import org.rc.platform.testagent.application.wrapper.MenuWrapper;
import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 菜单(Menu)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:38
 */
@RestController
@RequestMapping("/menu")
@Tag(name = "Menu", description = "菜单接口")
public class MenuController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private MenuService menuService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入menu")
    public R<MenuVO> detail(Menu menu) {
        Menu detail = menuService.getOne(Condition.getQueryWrapper(menu));
        return R.data(MenuWrapper.build().entityVO(detail));
    }

    /**
     * 分页 menu
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入menu")
    public R<IPage<MenuVO>> list(Menu menu, Query query) {
        IPage<Menu> pages = menuService.page(Condition.getPage(query), Condition.getQueryWrapper(menu));
        return R.data(MenuWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 menu
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入menu")
    public R<IPage<MenuVO>> page(MenuVO menu, Query query) {
        IPage<MenuVO> pages = menuService.selectMenuPage(Condition.getPage(query), menu);
        return R.data(pages);
    }

    /**
     * 新增 menu
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入menu")
    public R save(@Valid @RequestBody Menu menu) {
        return R.status(menuService.save(menu));
    }

    /**
     * 修改 menu
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入menu")
    public R update(@Valid @RequestBody Menu menu) {
        return R.status(menuService.updateById(menu));
    }

    /**
     * 新增或修改 menu
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入menu")
    public R submit(@Valid @RequestBody Menu menu) {
        return R.status(menuService.saveOrUpdate(menu));
    }


    /**
     * 删除 menu
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(menuService.deleteLogic(Func.toLongList(ids)));
    }


}
