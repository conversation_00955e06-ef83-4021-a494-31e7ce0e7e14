package org.rc.platform.testagent.application.service;


import org.rc.platform.testagent.application.entity.Env;
import org.rc.platform.testagent.application.vo.EnvVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 应用环境(Env)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:14:41
 */
public interface EnvService extends BaseService<Env> {

    /**
     * 自定义分页
     *
     * @param page
     * @param env
     * @return
     */
    IPage<EnvVO> selectEnvPage(IPage<EnvVO> page, EnvVO env);
}
