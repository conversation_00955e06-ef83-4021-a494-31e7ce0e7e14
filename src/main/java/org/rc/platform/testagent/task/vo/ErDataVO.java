package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.ErData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * ER数据(ErData)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "ER数据")
public class ErDataVO extends ErData {
    private static final long serialVersionUID = 1L;

}

