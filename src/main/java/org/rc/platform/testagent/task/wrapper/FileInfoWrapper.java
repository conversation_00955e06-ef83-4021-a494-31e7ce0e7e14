/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.FileInfo;
import org.rc.platform.testagent.task.vo.FileInfoVO;


/**
 * 文件信息(FileInfo)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:22
 */
public class FileInfoWrapper extends BaseEntityWrapper<FileInfo, FileInfoVO> {

    public static FileInfoWrapper build() {
        return new FileInfoWrapper();
    }

    @Override
    public FileInfoVO entityVO(FileInfo fileInfo) {
        FileInfoVO fileInfoVO = BeanUtil.copyProperties(fileInfo, FileInfoVO.class);

        return fileInfoVO;
    }

}

