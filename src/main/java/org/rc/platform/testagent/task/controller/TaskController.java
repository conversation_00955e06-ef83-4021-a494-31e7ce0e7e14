package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.Task;

import org.rc.platform.testagent.task.vo.TaskVO;
import org.rc.platform.testagent.task.wrapper.TaskWrapper;


import org.rc.platform.testagent.task.service.TaskService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * AI任务表(Task)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:24
 */
@RestController
@RequestMapping("/task")
@Tag(name = "Task", description = "AI任务表接口")
public class TaskController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private TaskService taskService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入task")
    public R<TaskVO> detail(Task task) {
        Task detail = taskService.getOne(Condition.getQueryWrapper(task));
        return R.data(TaskWrapper.build().entityVO(detail));
    }

    /**
     * 分页 task
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入task")
    public R<IPage<TaskVO>> list(Task task, Query query) {
        IPage<Task> pages = taskService.page(Condition.getPage(query), Condition.getQueryWrapper(task));
        return R.data(TaskWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 task
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入task")
    public R<IPage<TaskVO>> page(TaskVO task, Query query) {
        IPage<TaskVO> pages = taskService.selectTaskPage(Condition.getPage(query), task);
        return R.data(pages);
    }

    /**
     * 新增 task
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入task")
    public R save(@Valid @RequestBody Task task) {
        return R.status(taskService.save(task));
    }

    /**
     * 修改 task
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入task")
    public R update(@Valid @RequestBody Task task) {
        return R.status(taskService.updateById(task));
    }

    /**
     * 新增或修改 task
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入task")
    public R submit(@Valid @RequestBody Task task) {
        return R.status(taskService.saveOrUpdate(task));
    }


    /**
     * 删除 task
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(taskService.deleteLogic(Func.toLongList(ids)));
    }


}
