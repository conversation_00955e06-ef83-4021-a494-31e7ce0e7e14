package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.FileLog;
import org.rc.platform.testagent.task.mapper.FileLogMapper;
import org.rc.platform.testagent.task.vo.FileLogVO;
import org.rc.platform.testagent.task.service.FileLogService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 文件生成记录表(FileLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:23
 */
@Service
public class FileLogServiceImpl extends BaseServiceImpl<FileLogMapper, FileLog> implements FileLogService {

    @Override
    public IPage<FileLogVO> selectFileLogPage(IPage<FileLogVO> page, FileLogVO fileLog) {
        return page.setRecords(baseMapper.selectFileLogPage(page, fileLog));
    }

}
