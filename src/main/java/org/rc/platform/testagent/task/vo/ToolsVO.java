package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.Tools;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 工具(Tools)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "工具")
public class ToolsVO extends Tools {
    private static final long serialVersionUID = 1L;

}

