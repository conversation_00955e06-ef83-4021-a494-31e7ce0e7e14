package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.ErData;
import org.rc.platform.testagent.task.mapper.ErDataMapper;
import org.rc.platform.testagent.task.vo.ErDataVO;
import org.rc.platform.testagent.task.service.ErDataService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * ER数据(ErData)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:20
 */
@Service
public class ErDataServiceImpl extends BaseServiceImpl<ErDataMapper, ErData> implements ErDataService {

    @Override
    public IPage<ErDataVO> selectErDataPage(IPage<ErDataVO> page, ErDataVO erData) {
        return page.setRecords(baseMapper.selectErDataPage(page, erData));
    }

}
