package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.FileEvaluate;
import org.rc.platform.testagent.task.mapper.FileEvaluateMapper;
import org.rc.platform.testagent.task.vo.FileEvaluateVO;
import org.rc.platform.testagent.task.service.FileEvaluateService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 文件结果评估表(FileEvaluate)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:21
 */
@Service
public class FileEvaluateServiceImpl extends BaseServiceImpl<FileEvaluateMapper, FileEvaluate> implements FileEvaluateService {

    @Override
    public IPage<FileEvaluateVO> selectFileEvaluatePage(IPage<FileEvaluateVO> page, FileEvaluateVO fileEvaluate) {
        return page.setRecords(baseMapper.selectFileEvaluatePage(page, fileEvaluate));
    }

}
