/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.FileLog;
import org.rc.platform.testagent.task.vo.FileLogVO;


/**
 * 文件生成记录表(FileLog)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:23
 */
public class FileLogWrapper extends BaseEntityWrapper<FileLog, FileLogVO> {

    public static FileLogWrapper build() {
        return new FileLogWrapper();
    }

    @Override
    public FileLogVO entityVO(FileLog fileLog) {
        FileLogVO fileLogVO = BeanUtil.copyProperties(fileLog, FileLogVO.class);

        return fileLogVO;
    }

}

