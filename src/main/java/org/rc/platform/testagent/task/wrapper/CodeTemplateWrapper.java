/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.CodeTemplate;
import org.rc.platform.testagent.task.vo.CodeTemplateVO;


/**
 * 代码模板(CodeTemplate)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:16
 */
public class CodeTemplateWrapper extends BaseEntityWrapper<CodeTemplate, CodeTemplateVO> {

    public static CodeTemplateWrapper build() {
        return new CodeTemplateWrapper();
    }

    @Override
    public CodeTemplateVO entityVO(CodeTemplate codeTemplate) {
        CodeTemplateVO codeTemplateVO = BeanUtil.copyProperties(codeTemplate, CodeTemplateVO.class);

        return codeTemplateVO;
    }

}

