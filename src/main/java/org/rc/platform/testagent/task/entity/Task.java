package org.rc.platform.testagent.task.entity;

import java.util.Date;


import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * AI任务表(Task)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:24
 */
@Data
@TableName(value = "ai_task")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class Task extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 上一级任务
     */
    @Schema(description = "上一级任务")
    private Long parentId;
    /**
     * 会话内容
     */
    @Schema(description = "会话内容")
    private Long chatContentId;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 任务类型0同步任务|1异步任务，默认0
     */
    @Schema(description = "任务类型0同步任务|1异步任务，默认0")
    private Integer taskType;
    /**
     * 任务状态-1待处理|2处理中|3成功|4失败|5已过期，默认0
     */
    @Schema(description = "任务状态-1待处理|2处理中|3成功|4失败|5已过期，默认0")
    private Integer taskStatus;
    /**
     * 任务执行方式-0串行|1并发，默认0
     */
    @Schema(description = "任务执行方式-0串行|1并发，默认0")
    private Integer actionType;
    /**
     * 优先级，数字越大优先级越高【0,，99】默认0
     */
    @Schema(description = "优先级，数字越大优先级越高【0,，99】默认0")
    private Integer priority;
    /**
     * 返回码
     */
    @Schema(description = "返回码")
    private String resultCode;
    /**
     * 返回消息
     */
    @Schema(description = "返回消息")
    private String resultMessage;
    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private Date expireTime;
    /**
     * 执行ID
     */
    @Schema(description = "执行ID")
    private String executeId;
    /**
     * 执行次数
     */
    @Schema(description = "执行次数")
    private Integer executeCount;
    /**
     * 业务参数
     */
    @Schema(description = "业务参数")
    private String businessParam;
    /**
     * 响应参数
     */
    @Schema(description = "响应参数")
    private String responseParam;
    /**
     * 扩展信息
     */
    @Schema(description = "扩展信息")
    private String extInfo;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 意图编码
     */
    @Schema(description = "意图编码")
    private String intentCode;

}
