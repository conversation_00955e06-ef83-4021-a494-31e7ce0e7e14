<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.task.mapper.TaskMapper">

    <resultMap type="org.rc.platform.testagent.task.entity.Task" id="taskResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="chatContentId" column="chat_content_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="INTEGER"/>
        <result property="taskStatus" column="task_status" jdbcType="INTEGER"/>
        <result property="actionType" column="action_type" jdbcType="INTEGER"/>
        <result property="priority" column="priority" jdbcType="INTEGER"/>
        <result property="resultCode" column="result_code" jdbcType="VARCHAR"/>
        <result property="resultMessage" column="result_message" jdbcType="VARCHAR"/>
        <result property="expireTime" column="expire_time" jdbcType="TIMESTAMP"/>
        <result property="executeId" column="execute_id" jdbcType="VARCHAR"/>
        <result property="executeCount" column="execute_count" jdbcType="INTEGER"/>
        <result property="businessParam" column="business_param" jdbcType="VARCHAR"/>
        <result property="responseParam" column="response_param" jdbcType="VARCHAR"/>
        <result property="extInfo" column="ext_info" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="intentCode" column="intent_code" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_task(parent_id, chat_content_id, name, description, task_type, task_status, action_type,
        priority, result_code, result_message, expire_time, execute_id, execute_count, business_param, response_param,
        ext_info, status, create_user, update_user, create_time, update_time, is_deleted, remark, sort, tenant_id,
        version, intent_code)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.parentId}, #{entity.chatContentId}, #{entity.name}, #{entity.description}, #{entity.taskType},
            #{entity.taskStatus}, #{entity.actionType}, #{entity.priority}, #{entity.resultCode},
            #{entity.resultMessage}, #{entity.expireTime}, #{entity.executeId}, #{entity.executeCount},
            #{entity.businessParam}, #{entity.responseParam}, #{entity.extInfo}, #{entity.status}, #{entity.createUser},
            #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark},
            #{entity.sort}, #{entity.tenantId}, #{entity.version}, #{entity.intentCode})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_task(parent_id, chat_content_id, name, description, task_type, task_status, action_type,
        priority, result_code, result_message, expire_time, execute_id, execute_count, business_param, response_param,
        ext_info, status, create_user, update_user, create_time, update_time, is_deleted, remark, sort, tenant_id,
        version, intent_code)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.parentId}, #{entity.chatContentId}, #{entity.name}, #{entity.description}, #{entity.taskType},
            #{entity.taskStatus}, #{entity.actionType}, #{entity.priority}, #{entity.resultCode},
            #{entity.resultMessage}, #{entity.expireTime}, #{entity.executeId}, #{entity.executeCount},
            #{entity.businessParam}, #{entity.responseParam}, #{entity.extInfo}, #{entity.status}, #{entity.createUser},
            #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark},
            #{entity.sort}, #{entity.tenantId}, #{entity.version}, #{entity.intentCode})
        </foreach>
        on duplicate key update
        parent_id = values(parent_id) , chat_content_id = values(chat_content_id) , name = values(name) , description =
        values(description) , task_type = values(task_type) , task_status = values(task_status) , action_type =
        values(action_type) , priority = values(priority) , result_code = values(result_code) , result_message =
        values(result_message) , expire_time = values(expire_time) , execute_id = values(execute_id) , execute_count =
        values(execute_count) , business_param = values(business_param) , response_param = values(response_param) ,
        ext_info = values(ext_info) , status = values(status) , create_user = values(create_user) , update_user =
        values(update_user) , create_time = values(create_time) , update_time = values(update_time) , is_deleted =
        values(is_deleted) , remark = values(remark) , sort = values(sort) , tenant_id = values(tenant_id) , version =
        values(version) , intent_code = values(intent_code)
    </insert>
    <select id="selectTaskPage" resultMap="taskResultMap">
        select *
        from ai_task
        where is_deleted = 0
    </select>

</mapper>
