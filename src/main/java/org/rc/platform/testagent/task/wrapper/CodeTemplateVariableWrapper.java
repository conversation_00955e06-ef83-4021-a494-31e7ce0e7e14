/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.CodeTemplateVariable;
import org.rc.platform.testagent.task.vo.CodeTemplateVariableVO;


/**
 * 代码模板变量(CodeTemplateVariable)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:18
 */
public class CodeTemplateVariableWrapper extends BaseEntityWrapper<CodeTemplateVariable, CodeTemplateVariableVO> {

    public static CodeTemplateVariableWrapper build() {
        return new CodeTemplateVariableWrapper();
    }

    @Override
    public CodeTemplateVariableVO entityVO(CodeTemplateVariable codeTemplateVariable) {
        CodeTemplateVariableVO codeTemplateVariableVO = BeanUtil.copyProperties(codeTemplateVariable, CodeTemplateVariableVO.class);

        return codeTemplateVariableVO;
    }

}

