package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.FileLog;

import org.rc.platform.testagent.task.vo.FileLogVO;
import org.rc.platform.testagent.task.wrapper.FileLogWrapper;


import org.rc.platform.testagent.task.service.FileLogService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 文件生成记录表(FileLog)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:23
 */
@RestController
@RequestMapping("/fileLog")
@Tag(name = "FileLog", description = "文件生成记录表接口")
public class FileLogController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private FileLogService fileLogService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入fileLog")
    public R<FileLogVO> detail(FileLog fileLog) {
        FileLog detail = fileLogService.getOne(Condition.getQueryWrapper(fileLog));
        return R.data(FileLogWrapper.build().entityVO(detail));
    }

    /**
     * 分页 fileLog
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入fileLog")
    public R<IPage<FileLogVO>> list(FileLog fileLog, Query query) {
        IPage<FileLog> pages = fileLogService.page(Condition.getPage(query), Condition.getQueryWrapper(fileLog));
        return R.data(FileLogWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 fileLog
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入fileLog")
    public R<IPage<FileLogVO>> page(FileLogVO fileLog, Query query) {
        IPage<FileLogVO> pages = fileLogService.selectFileLogPage(Condition.getPage(query), fileLog);
        return R.data(pages);
    }

    /**
     * 新增 fileLog
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入fileLog")
    public R save(@Valid @RequestBody FileLog fileLog) {
        return R.status(fileLogService.save(fileLog));
    }

    /**
     * 修改 fileLog
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入fileLog")
    public R update(@Valid @RequestBody FileLog fileLog) {
        return R.status(fileLogService.updateById(fileLog));
    }

    /**
     * 新增或修改 fileLog
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入fileLog")
    public R submit(@Valid @RequestBody FileLog fileLog) {
        return R.status(fileLogService.saveOrUpdate(fileLog));
    }


    /**
     * 删除 fileLog
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(fileLogService.deleteLogic(Func.toLongList(ids)));
    }


}
