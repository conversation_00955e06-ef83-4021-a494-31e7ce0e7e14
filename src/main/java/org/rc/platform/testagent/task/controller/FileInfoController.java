package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.FileInfo;

import org.rc.platform.testagent.task.vo.FileInfoVO;
import org.rc.platform.testagent.task.wrapper.FileInfoWrapper;


import org.rc.platform.testagent.task.service.FileInfoService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 文件信息(FileInfo)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:22
 */
@RestController
@RequestMapping("/fileInfo")
@Tag(name = "FileInfo", description = "文件信息接口")
public class FileInfoController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private FileInfoService fileInfoService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入fileInfo")
    public R<FileInfoVO> detail(FileInfo fileInfo) {
        FileInfo detail = fileInfoService.getOne(Condition.getQueryWrapper(fileInfo));
        return R.data(FileInfoWrapper.build().entityVO(detail));
    }

    /**
     * 分页 fileInfo
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入fileInfo")
    public R<IPage<FileInfoVO>> list(FileInfo fileInfo, Query query) {
        IPage<FileInfo> pages = fileInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(fileInfo));
        return R.data(FileInfoWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 fileInfo
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入fileInfo")
    public R<IPage<FileInfoVO>> page(FileInfoVO fileInfo, Query query) {
        IPage<FileInfoVO> pages = fileInfoService.selectFileInfoPage(Condition.getPage(query), fileInfo);
        return R.data(pages);
    }

    /**
     * 新增 fileInfo
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入fileInfo")
    public R save(@Valid @RequestBody FileInfo fileInfo) {
        return R.status(fileInfoService.save(fileInfo));
    }

    /**
     * 修改 fileInfo
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入fileInfo")
    public R update(@Valid @RequestBody FileInfo fileInfo) {
        return R.status(fileInfoService.updateById(fileInfo));
    }

    /**
     * 新增或修改 fileInfo
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入fileInfo")
    public R submit(@Valid @RequestBody FileInfo fileInfo) {
        return R.status(fileInfoService.saveOrUpdate(fileInfo));
    }


    /**
     * 删除 fileInfo
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(fileInfoService.deleteLogic(Func.toLongList(ids)));
    }


}
