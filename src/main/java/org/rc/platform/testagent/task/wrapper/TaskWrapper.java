/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.Task;
import org.rc.platform.testagent.task.vo.TaskVO;


/**
 * AI任务表(Task)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:25
 */
public class TaskWrapper extends BaseEntityWrapper<Task, TaskVO> {

    public static TaskWrapper build() {
        return new TaskWrapper();
    }

    @Override
    public TaskVO entityVO(Task task) {
        TaskVO taskVO = BeanUtil.copyProperties(task, TaskVO.class);

        return taskVO;
    }

}

