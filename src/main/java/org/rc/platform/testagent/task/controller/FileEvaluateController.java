package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.FileEvaluate;

import org.rc.platform.testagent.task.vo.FileEvaluateVO;
import org.rc.platform.testagent.task.wrapper.FileEvaluateWrapper;


import org.rc.platform.testagent.task.service.FileEvaluateService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 文件结果评估表(FileEvaluate)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:21
 */
@RestController
@RequestMapping("/fileEvaluate")
@Tag(name = "FileEvaluate", description = "文件结果评估表接口")
public class FileEvaluateController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private FileEvaluateService fileEvaluateService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入fileEvaluate")
    public R<FileEvaluateVO> detail(FileEvaluate fileEvaluate) {
        FileEvaluate detail = fileEvaluateService.getOne(Condition.getQueryWrapper(fileEvaluate));
        return R.data(FileEvaluateWrapper.build().entityVO(detail));
    }

    /**
     * 分页 fileEvaluate
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入fileEvaluate")
    public R<IPage<FileEvaluateVO>> list(FileEvaluate fileEvaluate, Query query) {
        IPage<FileEvaluate> pages = fileEvaluateService.page(Condition.getPage(query), Condition.getQueryWrapper(fileEvaluate));
        return R.data(FileEvaluateWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 fileEvaluate
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入fileEvaluate")
    public R<IPage<FileEvaluateVO>> page(FileEvaluateVO fileEvaluate, Query query) {
        IPage<FileEvaluateVO> pages = fileEvaluateService.selectFileEvaluatePage(Condition.getPage(query), fileEvaluate);
        return R.data(pages);
    }

    /**
     * 新增 fileEvaluate
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入fileEvaluate")
    public R save(@Valid @RequestBody FileEvaluate fileEvaluate) {
        return R.status(fileEvaluateService.save(fileEvaluate));
    }

    /**
     * 修改 fileEvaluate
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入fileEvaluate")
    public R update(@Valid @RequestBody FileEvaluate fileEvaluate) {
        return R.status(fileEvaluateService.updateById(fileEvaluate));
    }

    /**
     * 新增或修改 fileEvaluate
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入fileEvaluate")
    public R submit(@Valid @RequestBody FileEvaluate fileEvaluate) {
        return R.status(fileEvaluateService.saveOrUpdate(fileEvaluate));
    }


    /**
     * 删除 fileEvaluate
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(fileEvaluateService.deleteLogic(Func.toLongList(ids)));
    }


}
