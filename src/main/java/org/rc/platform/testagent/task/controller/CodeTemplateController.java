package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.CodeTemplate;

import org.rc.platform.testagent.task.vo.CodeTemplateVO;
import org.rc.platform.testagent.task.wrapper.CodeTemplateWrapper;


import org.rc.platform.testagent.task.service.CodeTemplateService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 代码模板(CodeTemplate)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:16
 */
@RestController
@RequestMapping("/codeTemplate")
@Tag(name = "CodeTemplate", description = "代码模板接口")
public class CodeTemplateController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private CodeTemplateService codeTemplateService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入codeTemplate")
    public R<CodeTemplateVO> detail(CodeTemplate codeTemplate) {
        CodeTemplate detail = codeTemplateService.getOne(Condition.getQueryWrapper(codeTemplate));
        return R.data(CodeTemplateWrapper.build().entityVO(detail));
    }

    /**
     * 分页 codeTemplate
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入codeTemplate")
    public R<IPage<CodeTemplateVO>> list(CodeTemplate codeTemplate, Query query) {
        IPage<CodeTemplate> pages = codeTemplateService.page(Condition.getPage(query), Condition.getQueryWrapper(codeTemplate));
        return R.data(CodeTemplateWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 codeTemplate
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入codeTemplate")
    public R<IPage<CodeTemplateVO>> page(CodeTemplateVO codeTemplate, Query query) {
        IPage<CodeTemplateVO> pages = codeTemplateService.selectCodeTemplatePage(Condition.getPage(query), codeTemplate);
        return R.data(pages);
    }

    /**
     * 新增 codeTemplate
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入codeTemplate")
    public R save(@Valid @RequestBody CodeTemplate codeTemplate) {
        return R.status(codeTemplateService.save(codeTemplate));
    }

    /**
     * 修改 codeTemplate
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入codeTemplate")
    public R update(@Valid @RequestBody CodeTemplate codeTemplate) {
        return R.status(codeTemplateService.updateById(codeTemplate));
    }

    /**
     * 新增或修改 codeTemplate
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入codeTemplate")
    public R submit(@Valid @RequestBody CodeTemplate codeTemplate) {
        return R.status(codeTemplateService.saveOrUpdate(codeTemplate));
    }


    /**
     * 删除 codeTemplate
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(codeTemplateService.deleteLogic(Func.toLongList(ids)));
    }


}
