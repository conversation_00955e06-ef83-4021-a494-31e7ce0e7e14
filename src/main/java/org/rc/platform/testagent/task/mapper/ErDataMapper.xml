<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.task.mapper.ErDataMapper">

    <resultMap type="org.rc.platform.testagent.task.entity.ErData" id="erDataResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="chatContentId" column="chat_content_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="erData" column="er_data" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_er_data(chat_content_id, name, description, status, create_user, update_user,
        create_time, update_time, is_deleted, remark, sort, tenant_id, version, er_data)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatContentId}, #{entity.name}, #{entity.description}, #{entity.status}, #{entity.createUser},
            #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark},
            #{entity.sort}, #{entity.tenantId}, #{entity.version}, #{entity.erData})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_er_data(chat_content_id, name, description, status, create_user, update_user,
        create_time, update_time, is_deleted, remark, sort, tenant_id, version, er_data)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatContentId}, #{entity.name}, #{entity.description}, #{entity.status}, #{entity.createUser},
            #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark},
            #{entity.sort}, #{entity.tenantId}, #{entity.version}, #{entity.erData})
        </foreach>
        on duplicate key update
        chat_content_id = values(chat_content_id) , name = values(name) , description = values(description) , status =
        values(status) , create_user = values(create_user) , update_user = values(update_user) , create_time =
        values(create_time) , update_time = values(update_time) , is_deleted = values(is_deleted) , remark =
        values(remark) , sort = values(sort) , tenant_id = values(tenant_id) , version = values(version) , er_data =
        values(er_data)
    </insert>
    <select id="selectErDataPage" resultMap="erDataResultMap">
        select *
        from ai_er_data
        where is_deleted = 0
    </select>

</mapper>
