package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.CodeTemplateVariable;
import org.rc.platform.testagent.task.mapper.CodeTemplateVariableMapper;
import org.rc.platform.testagent.task.vo.CodeTemplateVariableVO;
import org.rc.platform.testagent.task.service.CodeTemplateVariableService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 代码模板变量(CodeTemplateVariable)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:17
 */
@Service
public class CodeTemplateVariableServiceImpl extends BaseServiceImpl<CodeTemplateVariableMapper, CodeTemplateVariable> implements CodeTemplateVariableService {

    @Override
    public IPage<CodeTemplateVariableVO> selectCodeTemplateVariablePage(IPage<CodeTemplateVariableVO> page, CodeTemplateVariableVO codeTemplateVariable) {
        return page.setRecords(baseMapper.selectCodeTemplateVariablePage(page, codeTemplateVariable));
    }

}
