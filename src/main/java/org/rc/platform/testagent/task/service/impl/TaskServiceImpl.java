package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.Task;
import org.rc.platform.testagent.task.mapper.TaskMapper;
import org.rc.platform.testagent.task.vo.TaskVO;
import org.rc.platform.testagent.task.service.TaskService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI任务表(Task)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:25
 */
@Service
public class TaskServiceImpl extends BaseServiceImpl<TaskMapper, Task> implements TaskService {

    @Override
    public IPage<TaskVO> selectTaskPage(IPage<TaskVO> page, TaskVO task) {
        return page.setRecords(baseMapper.selectTaskPage(page, task));
    }

}
