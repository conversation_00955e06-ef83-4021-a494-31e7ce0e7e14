package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.ErData;

import org.rc.platform.testagent.task.vo.ErDataVO;
import org.rc.platform.testagent.task.wrapper.ErDataWrapper;


import org.rc.platform.testagent.task.service.ErDataService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * ER数据(ErData)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:18
 */
@RestController
@RequestMapping("/erData")
@Tag(name = "ErData", description = "ER数据接口")
public class ErDataController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ErDataService erDataService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入erData")
    public R<ErDataVO> detail(ErData erData) {
        ErData detail = erDataService.getOne(Condition.getQueryWrapper(erData));
        return R.data(ErDataWrapper.build().entityVO(detail));
    }

    /**
     * 分页 erData
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入erData")
    public R<IPage<ErDataVO>> list(ErData erData, Query query) {
        IPage<ErData> pages = erDataService.page(Condition.getPage(query), Condition.getQueryWrapper(erData));
        return R.data(ErDataWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 erData
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入erData")
    public R<IPage<ErDataVO>> page(ErDataVO erData, Query query) {
        IPage<ErDataVO> pages = erDataService.selectErDataPage(Condition.getPage(query), erData);
        return R.data(pages);
    }

    /**
     * 新增 erData
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入erData")
    public R save(@Valid @RequestBody ErData erData) {
        return R.status(erDataService.save(erData));
    }

    /**
     * 修改 erData
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入erData")
    public R update(@Valid @RequestBody ErData erData) {
        return R.status(erDataService.updateById(erData));
    }

    /**
     * 新增或修改 erData
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入erData")
    public R submit(@Valid @RequestBody ErData erData) {
        return R.status(erDataService.saveOrUpdate(erData));
    }


    /**
     * 删除 erData
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(erDataService.deleteLogic(Func.toLongList(ids)));
    }


}
