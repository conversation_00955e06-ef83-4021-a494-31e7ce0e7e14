package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.FileInfo;
import org.rc.platform.testagent.task.vo.FileInfoVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 文件信息(FileInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:22
 */
public interface FileInfoService extends BaseService<FileInfo> {

    /**
     * 自定义分页
     *
     * @param page
     * @param fileInfo
     * @return
     */
    IPage<FileInfoVO> selectFileInfoPage(IPage<FileInfoVO> page, FileInfoVO fileInfo);
}
