package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.FileLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 文件生成记录表(FileLog)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件生成记录表")
public class FileLogVO extends FileLog {
    private static final long serialVersionUID = 1L;

}

