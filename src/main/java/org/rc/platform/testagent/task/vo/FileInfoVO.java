package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.FileInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 文件信息(FileInfo)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件信息")
public class FileInfoVO extends FileInfo {
    private static final long serialVersionUID = 1L;

}

