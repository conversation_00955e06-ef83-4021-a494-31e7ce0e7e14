package org.rc.platform.testagent.task.mapper;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.rc.platform.testagent.task.entity.FileLog;

import org.rc.platform.testagent.task.vo.FileLogVO;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 文件生成记录表(FileLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:23
 */
public interface FileLogMapper extends BaseMapper<FileLog> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<FileLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<FileLog> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<FileLog> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<FileLog> entities);

    /**
     * 自定义分页
     *
     * @param page
     * @param fileLog
     * @return
     */
    List<FileLogVO> selectFileLogPage(IPage page, FileLogVO fileLog);

}
