package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.CodeTemplate;
import org.rc.platform.testagent.task.vo.CodeTemplateVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 代码模板(CodeTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:16
 */
public interface CodeTemplateService extends BaseService<CodeTemplate> {

    /**
     * 自定义分页
     *
     * @param page
     * @param codeTemplate
     * @return
     */
    IPage<CodeTemplateVO> selectCodeTemplatePage(IPage<CodeTemplateVO> page, CodeTemplateVO codeTemplate);
}
