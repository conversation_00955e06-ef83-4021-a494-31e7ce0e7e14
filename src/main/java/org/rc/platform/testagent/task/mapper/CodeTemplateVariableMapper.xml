<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.task.mapper.CodeTemplateVariableMapper">

    <resultMap type="org.rc.platform.testagent.task.entity.CodeTemplateVariable" id="codeTemplateVariableResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="codeTplId" column="code_tpl_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="defaultValue" column="default_value" jdbcType="VARCHAR"/>
        <result property="isRequired" column="is_required" jdbcType="INTEGER"/>
        <result property="allowedValues" column="allowed_values" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_code_template_variable(code_tpl_id, status, create_user, update_user, create_time,
        update_time, is_deleted, remark, sort, tenant_id, version, name, type, description, default_value, is_required,
        allowed_values)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.codeTplId}, #{entity.status}, #{entity.createUser}, #{entity.updateUser}, #{entity.createTime},
            #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark}, #{entity.sort}, #{entity.tenantId},
            #{entity.version}, #{entity.name}, #{entity.type}, #{entity.description}, #{entity.defaultValue},
            #{entity.isRequired}, #{entity.allowedValues})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_code_template_variable(code_tpl_id, status, create_user, update_user, create_time,
        update_time, is_deleted, remark, sort, tenant_id, version, name, type, description, default_value, is_required,
        allowed_values)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.codeTplId}, #{entity.status}, #{entity.createUser}, #{entity.updateUser}, #{entity.createTime},
            #{entity.updateTime}, #{entity.isDeleted}, #{entity.remark}, #{entity.sort}, #{entity.tenantId},
            #{entity.version}, #{entity.name}, #{entity.type}, #{entity.description}, #{entity.defaultValue},
            #{entity.isRequired}, #{entity.allowedValues})
        </foreach>
        on duplicate key update
        code_tpl_id = values(code_tpl_id) , status = values(status) , create_user = values(create_user) , update_user =
        values(update_user) , create_time = values(create_time) , update_time = values(update_time) , is_deleted =
        values(is_deleted) , remark = values(remark) , sort = values(sort) , tenant_id = values(tenant_id) , version =
        values(version) , name = values(name) , type = values(type) , description = values(description) , default_value
        = values(default_value) , is_required = values(is_required) , allowed_values = values(allowed_values)
    </insert>
    <select id="selectCodeTemplateVariablePage" resultMap="codeTemplateVariableResultMap">
        select *
        from ai_code_template_variable
        where is_deleted = 0
    </select>

</mapper>
