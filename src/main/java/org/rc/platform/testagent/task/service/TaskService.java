package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.Task;
import org.rc.platform.testagent.task.vo.TaskVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * AI任务表(Task)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:24
 */
public interface TaskService extends BaseService<Task> {

    /**
     * 自定义分页
     *
     * @param page
     * @param task
     * @return
     */
    IPage<TaskVO> selectTaskPage(IPage<TaskVO> page, TaskVO task);
}
