package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.CodeTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 代码模板(CodeTemplate)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "代码模板")
public class CodeTemplateVO extends CodeTemplate {
    private static final long serialVersionUID = 1L;

}

