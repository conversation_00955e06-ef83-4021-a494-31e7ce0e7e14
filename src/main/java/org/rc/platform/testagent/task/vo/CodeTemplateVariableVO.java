package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.CodeTemplateVariable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 代码模板变量(CodeTemplateVariable)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "代码模板变量")
public class CodeTemplateVariableVO extends CodeTemplateVariable {
    private static final long serialVersionUID = 1L;

}

