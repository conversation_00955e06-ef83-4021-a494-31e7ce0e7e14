/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.FileEvaluate;
import org.rc.platform.testagent.task.vo.FileEvaluateVO;


/**
 * 文件结果评估表(FileEvaluate)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:21
 */
public class FileEvaluateWrapper extends BaseEntityWrapper<FileEvaluate, FileEvaluateVO> {

    public static FileEvaluateWrapper build() {
        return new FileEvaluateWrapper();
    }

    @Override
    public FileEvaluateVO entityVO(FileEvaluate fileEvaluate) {
        FileEvaluateVO fileEvaluateVO = BeanUtil.copyProperties(fileEvaluate, FileEvaluateVO.class);

        return fileEvaluateVO;
    }

}

