/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.Tools;
import org.rc.platform.testagent.task.vo.ToolsVO;


/**
 * 工具(Tools)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:26
 */
public class ToolsWrapper extends BaseEntityWrapper<Tools, ToolsVO> {

    public static ToolsWrapper build() {
        return new ToolsWrapper();
    }

    @Override
    public ToolsVO entityVO(Tools tools) {
        ToolsVO toolsVO = BeanUtil.copyProperties(tools, ToolsVO.class);

        return toolsVO;
    }

}

