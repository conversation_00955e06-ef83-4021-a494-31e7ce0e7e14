package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.FileLog;
import org.rc.platform.testagent.task.vo.FileLogVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 文件生成记录表(FileLog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:23
 */
public interface FileLogService extends BaseService<FileLog> {

    /**
     * 自定义分页
     *
     * @param page
     * @param fileLog
     * @return
     */
    IPage<FileLogVO> selectFileLogPage(IPage<FileLogVO> page, FileLogVO fileLog);
}
