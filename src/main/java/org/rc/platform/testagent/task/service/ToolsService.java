package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.Tools;
import org.rc.platform.testagent.task.vo.ToolsVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 工具(Tools)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:26
 */
public interface ToolsService extends BaseService<Tools> {

    /**
     * 自定义分页
     *
     * @param page
     * @param tools
     * @return
     */
    IPage<ToolsVO> selectToolsPage(IPage<ToolsVO> page, ToolsVO tools);
}
