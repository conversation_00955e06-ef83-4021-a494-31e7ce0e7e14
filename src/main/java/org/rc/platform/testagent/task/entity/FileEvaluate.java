package org.rc.platform.testagent.task.entity;

import java.util.Date;


import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.annotation.Version;

/**
 * 文件结果评估表(FileEvaluate)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:21
 */
@Data
@TableName(value = "ai_file_evaluate")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "对象")

public class FileEvaluate extends org.richinfo.framework.core.mp.base.BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 文件
     */
    @Schema(description = "文件")
    private Long fileId;
    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private Long conversationId;
    /**
     * 评估分数
     */
    @Schema(description = "评估分数")
    private Integer score;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 租户
     */
    @Schema(description = "租户")
    private Long tenantId;
    /**
     * 版本
     */
    @Schema(description = "版本")
    @Version
    private Integer version;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private Long updateUser;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

}
