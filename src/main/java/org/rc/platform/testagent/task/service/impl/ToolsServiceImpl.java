package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.Tools;
import org.rc.platform.testagent.task.mapper.ToolsMapper;
import org.rc.platform.testagent.task.vo.ToolsVO;
import org.rc.platform.testagent.task.service.ToolsService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 工具(Tools)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:26
 */
@Service
public class ToolsServiceImpl extends BaseServiceImpl<ToolsMapper, Tools> implements ToolsService {

    @Override
    public IPage<ToolsVO> selectToolsPage(IPage<ToolsVO> page, ToolsVO tools) {
        return page.setRecords(baseMapper.selectToolsPage(page, tools));
    }

}
