<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.rc.platform.testagent.task.mapper.FileLogMapper">

    <resultMap type="org.rc.platform.testagent.task.entity.FileLog" id="fileLogResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="taskId" column="task_id" jdbcType="INTEGER"/>
        <result property="conversationId" column="conversation_id" jdbcType="INTEGER"/>
        <result property="fileId" column="file_id" jdbcType="INTEGER"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileType" column="file_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_file_log(task_id, conversation_id, file_id, file_name, file_type, create_time,
        update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.taskId}, #{entity.conversationId}, #{entity.fileId}, #{entity.fileName}, #{entity.fileType},
            #{entity.createTime}, #{entity.updateTime}, #{entity.tenantId}, #{entity.version}, #{entity.createUser},
            #{entity.updateUser}, #{entity.isDeleted}, #{entity.status})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into richinfo.ai_file_log(task_id, conversation_id, file_id, file_name, file_type, create_time,
        update_time, tenant_id, version, create_user, update_user, is_deleted, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.taskId}, #{entity.conversationId}, #{entity.fileId}, #{entity.fileName}, #{entity.fileType},
            #{entity.createTime}, #{entity.updateTime}, #{entity.tenantId}, #{entity.version}, #{entity.createUser},
            #{entity.updateUser}, #{entity.isDeleted}, #{entity.status})
        </foreach>
        on duplicate key update
        task_id = values(task_id) , conversation_id = values(conversation_id) , file_id = values(file_id) , file_name =
        values(file_name) , file_type = values(file_type) , create_time = values(create_time) , update_time =
        values(update_time) , tenant_id = values(tenant_id) , version = values(version) , create_user =
        values(create_user) , update_user = values(update_user) , is_deleted = values(is_deleted) , status =
        values(status)
    </insert>
    <select id="selectFileLogPage" resultMap="fileLogResultMap">
        select *
        from ai_file_log
        where is_deleted = 0
    </select>

</mapper>
