package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.FileEvaluate;
import org.rc.platform.testagent.task.vo.FileEvaluateVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 文件结果评估表(FileEvaluate)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:21
 */
public interface FileEvaluateService extends BaseService<FileEvaluate> {

    /**
     * 自定义分页
     *
     * @param page
     * @param fileEvaluate
     * @return
     */
    IPage<FileEvaluateVO> selectFileEvaluatePage(IPage<FileEvaluateVO> page, FileEvaluateVO fileEvaluate);
}
