package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.Task;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI任务表(Task)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI任务表")
public class TaskVO extends Task {
    private static final long serialVersionUID = 1L;

}

