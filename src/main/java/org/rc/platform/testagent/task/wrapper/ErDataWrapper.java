/**
 * richinfo
 */


package org.rc.platform.testagent.task.wrapper;


import org.richinfo.framework.core.mp.support.BaseEntityWrapper;
import org.richinfo.framework.core.tool.utils.BeanUtil;
import org.rc.platform.testagent.task.entity.ErData;
import org.rc.platform.testagent.task.vo.ErDataVO;


/**
 * ER数据(ErData)包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:20
 */
public class ErDataWrapper extends BaseEntityWrapper<ErData, ErDataVO> {

    public static ErDataWrapper build() {
        return new ErDataWrapper();
    }

    @Override
    public ErDataVO entityVO(ErData erData) {
        ErDataVO erDataVO = BeanUtil.copyProperties(erData, ErDataVO.class);

        return erDataVO;
    }

}

