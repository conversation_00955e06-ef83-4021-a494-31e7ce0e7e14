package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.CodeTemplate;
import org.rc.platform.testagent.task.mapper.CodeTemplateMapper;
import org.rc.platform.testagent.task.vo.CodeTemplateVO;
import org.rc.platform.testagent.task.service.CodeTemplateService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 代码模板(CodeTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:16
 */
@Service
public class CodeTemplateServiceImpl extends BaseServiceImpl<CodeTemplateMapper, CodeTemplate> implements CodeTemplateService {

    @Override
    public IPage<CodeTemplateVO> selectCodeTemplatePage(IPage<CodeTemplateVO> page, CodeTemplateVO codeTemplate) {
        return page.setRecords(baseMapper.selectCodeTemplatePage(page, codeTemplate));
    }

}
