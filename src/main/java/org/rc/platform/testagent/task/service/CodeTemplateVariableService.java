package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.CodeTemplateVariable;
import org.rc.platform.testagent.task.vo.CodeTemplateVariableVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 代码模板变量(CodeTemplateVariable)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:17
 */
public interface CodeTemplateVariableService extends BaseService<CodeTemplateVariable> {

    /**
     * 自定义分页
     *
     * @param page
     * @param codeTemplateVariable
     * @return
     */
    IPage<CodeTemplateVariableVO> selectCodeTemplateVariablePage(IPage<CodeTemplateVariableVO> page, CodeTemplateVariableVO codeTemplateVariable);
}
