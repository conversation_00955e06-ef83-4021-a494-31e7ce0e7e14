package org.rc.platform.testagent.task.dto;


import org.rc.platform.testagent.task.entity.CodeTemplateVariable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代码模板变量(CodeTemplateVariable)数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CodeTemplateVariableDTO extends CodeTemplateVariable {
    private static final long serialVersionUID = 1L;

}

