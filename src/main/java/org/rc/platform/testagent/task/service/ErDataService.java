package org.rc.platform.testagent.task.service;


import org.rc.platform.testagent.task.entity.ErData;
import org.rc.platform.testagent.task.vo.ErDataVO;

import org.richinfo.framework.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * ER数据(ErData)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:20
 */
public interface ErDataService extends BaseService<ErData> {

    /**
     * 自定义分页
     *
     * @param page
     * @param erData
     * @return
     */
    IPage<ErDataVO> selectErDataPage(IPage<ErDataVO> page, ErDataVO erData);
}
