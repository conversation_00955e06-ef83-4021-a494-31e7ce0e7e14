package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.CodeTemplateVariable;

import org.rc.platform.testagent.task.vo.CodeTemplateVariableVO;
import org.rc.platform.testagent.task.wrapper.CodeTemplateVariableWrapper;


import org.rc.platform.testagent.task.service.CodeTemplateVariableService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 代码模板变量(CodeTemplateVariable)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:17
 */
@RestController
@RequestMapping("/codeTemplateVariable")
@Tag(name = "CodeTemplateVariable", description = "代码模板变量接口")
public class CodeTemplateVariableController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private CodeTemplateVariableService codeTemplateVariableService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入codeTemplateVariable")
    public R<CodeTemplateVariableVO> detail(CodeTemplateVariable codeTemplateVariable) {
        CodeTemplateVariable detail = codeTemplateVariableService.getOne(Condition.getQueryWrapper(codeTemplateVariable));
        return R.data(CodeTemplateVariableWrapper.build().entityVO(detail));
    }

    /**
     * 分页 codeTemplateVariable
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入codeTemplateVariable")
    public R<IPage<CodeTemplateVariableVO>> list(CodeTemplateVariable codeTemplateVariable, Query query) {
        IPage<CodeTemplateVariable> pages = codeTemplateVariableService.page(Condition.getPage(query), Condition.getQueryWrapper(codeTemplateVariable));
        return R.data(CodeTemplateVariableWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 codeTemplateVariable
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入codeTemplateVariable")
    public R<IPage<CodeTemplateVariableVO>> page(CodeTemplateVariableVO codeTemplateVariable, Query query) {
        IPage<CodeTemplateVariableVO> pages = codeTemplateVariableService.selectCodeTemplateVariablePage(Condition.getPage(query), codeTemplateVariable);
        return R.data(pages);
    }

    /**
     * 新增 codeTemplateVariable
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入codeTemplateVariable")
    public R save(@Valid @RequestBody CodeTemplateVariable codeTemplateVariable) {
        return R.status(codeTemplateVariableService.save(codeTemplateVariable));
    }

    /**
     * 修改 codeTemplateVariable
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入codeTemplateVariable")
    public R update(@Valid @RequestBody CodeTemplateVariable codeTemplateVariable) {
        return R.status(codeTemplateVariableService.updateById(codeTemplateVariable));
    }

    /**
     * 新增或修改 codeTemplateVariable
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入codeTemplateVariable")
    public R submit(@Valid @RequestBody CodeTemplateVariable codeTemplateVariable) {
        return R.status(codeTemplateVariableService.saveOrUpdate(codeTemplateVariable));
    }


    /**
     * 删除 codeTemplateVariable
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(codeTemplateVariableService.deleteLogic(Func.toLongList(ids)));
    }


}
