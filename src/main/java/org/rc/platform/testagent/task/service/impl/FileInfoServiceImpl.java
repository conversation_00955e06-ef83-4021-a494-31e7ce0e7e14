package org.rc.platform.testagent.task.service.impl;


import org.rc.platform.testagent.task.entity.FileInfo;
import org.rc.platform.testagent.task.mapper.FileInfoMapper;
import org.rc.platform.testagent.task.vo.FileInfoVO;
import org.rc.platform.testagent.task.service.FileInfoService;
import org.springframework.stereotype.Service;

import org.richinfo.framework.core.mp.base.BaseServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;


/**
 * 文件信息(FileInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:22
 */
@Service
public class FileInfoServiceImpl extends BaseServiceImpl<FileInfoMapper, FileInfo> implements FileInfoService {

    @Override
    public IPage<FileInfoVO> selectFileInfoPage(IPage<FileInfoVO> page, FileInfoVO fileInfo) {
        return page.setRecords(baseMapper.selectFileInfoPage(page, fileInfo));
    }

}
