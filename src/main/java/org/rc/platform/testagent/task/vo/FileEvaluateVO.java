package org.rc.platform.testagent.task.vo;


import org.rc.platform.testagent.task.entity.FileEvaluate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 文件结果评估表(FileEvaluate)视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件结果评估表")
public class FileEvaluateVO extends FileEvaluate {
    private static final long serialVersionUID = 1L;

}

