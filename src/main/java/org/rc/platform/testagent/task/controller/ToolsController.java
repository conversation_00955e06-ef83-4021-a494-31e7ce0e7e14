package org.rc.platform.testagent.task.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.richinfo.framework.core.mp.support.Condition;
import org.richinfo.framework.core.mp.support.Query;
import org.richinfo.framework.core.tool.api.R;
import org.richinfo.framework.core.tool.utils.Func;

import com.baomidou.mybatisplus.core.metadata.IPage;


import org.rc.platform.testagent.task.entity.Tools;

import org.rc.platform.testagent.task.vo.ToolsVO;
import org.rc.platform.testagent.task.wrapper.ToolsWrapper;


import org.rc.platform.testagent.task.service.ToolsService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.richinfo.framework.core.boot.ctrl.RichinfoController;

/**
 * 工具(Tools)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-08 14:12:25
 */
@RestController
@RequestMapping("/tools")
@Tag(name = "Tools", description = "工具接口")
public class ToolsController extends RichinfoController {
    /**
     * 服务对象
     */
    @Autowired
    private ToolsService toolsService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入tools")
    public R<ToolsVO> detail(Tools tools) {
        Tools detail = toolsService.getOne(Condition.getQueryWrapper(tools));
        return R.data(ToolsWrapper.build().entityVO(detail));
    }

    /**
     * 分页 tools
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入tools")
    public R<IPage<ToolsVO>> list(Tools tools, Query query) {
        IPage<Tools> pages = toolsService.page(Condition.getPage(query), Condition.getQueryWrapper(tools));
        return R.data(ToolsWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 tools
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入tools")
    public R<IPage<ToolsVO>> page(ToolsVO tools, Query query) {
        IPage<ToolsVO> pages = toolsService.selectToolsPage(Condition.getPage(query), tools);
        return R.data(pages);
    }

    /**
     * 新增 tools
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入tools")
    public R save(@Valid @RequestBody Tools tools) {
        return R.status(toolsService.save(tools));
    }

    /**
     * 修改 tools
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入tools")
    public R update(@Valid @RequestBody Tools tools) {
        return R.status(toolsService.updateById(tools));
    }

    /**
     * 新增或修改 tools
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入tools")
    public R submit(@Valid @RequestBody Tools tools) {
        return R.status(toolsService.saveOrUpdate(tools));
    }


    /**
     * 删除 tools
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(name = "主键集合", required = true) @RequestParam String ids) {
        return R.status(toolsService.deleteLogic(Func.toLongList(ids)));
    }


}
