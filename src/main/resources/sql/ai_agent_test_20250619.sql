/*==============================================================*/
/* DBMS name:      MySQL 5.0                                    */
/* Created on:     2025/6/19 9:56:33                            */
/*==============================================================*/


/*==============================================================*/
/* Table: ai_agent_memory                                       */
/*==============================================================*/
create table ai_agent_memory
(
   id                   bigint not null  comment '主键',
   conversation_id      bigint  comment '会话ID',
   role                 varchar(20)  comment '角色',
   content              text  comment '内容',
   importance_score     double  comment '重要性评分',
   relevance_score      double  comment '相关性评分',
   is_critical          smallint  comment '是否关键',
   memory_type          smallint  comment '记忆类型-0会话|1知识库|2事件',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   is_deleted           int not null default 0  comment '是否删除',
   status               int not null default 0  comment '状态',
   primary key (id)
);

alter table ai_agent_memory comment '记忆实体';

/*==============================================================*/
/* Table: ai_agent_memory_meta                                  */
/*==============================================================*/
create table ai_agent_memory_meta
(
   id                   bigint not null  comment '主键',
   agent_memory_id      bigint  comment '记忆实体',
   memory_key           varchar(200)  comment '记忆key',
   memory_value         varchar(1000)  comment '记忆value',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   is_deleted           int not null default 0  comment '是否删除',
   status               int not null default 0  comment '状态',
   primary key (id)
);

alter table ai_agent_memory_meta comment '记忆体元数据';

/*==============================================================*/
/* Table: ai_application                                        */
/*==============================================================*/
create table ai_application
(
   id                   bigint not null  comment '主键',
   env_id               bigint  comment '环境ID',
   code                 varchar(32) not null default ' '  comment '编码',
   name                 varchar(100) not null default ' '  comment '名称',
   platform             varchar(1) not null default '1'  comment '平台-PC1|H5 2|Android 3|IOS 4|鸿蒙5',
   app_category         varchar(10) not null default ' '  comment '应用分类',
   description          varchar(200) not null default ' '  comment '描述',
   app_icon             varchar(100) not null default ' '  comment '应用图标',
   industry             smallint not null default 1  comment '1信息技术|2制造业|3零售业|4服务业|5信息技术|6医疗行业|7教育行业|8建筑业|9农业',
   business             smallint not null default 1  comment '业务功能-1运营管理|2财务管理|3市场营销|4人力资源|5客户服务',
   app_type             smallint not null default 1  comment '应用类型-1Web应用|2移动应用|3桌面应用|4集成应用',
   release_version      varchar(30) not null default ' '  comment '发布版本',
   app_version          int not null default 0  comment '应用版本',
   status               int not null default 0  comment '状态',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   is_deleted           int not null default 0  comment '是否删除',
   remark               varchar(100) not null default ' '  comment '备注',
   sort                 int default 0  comment '排序',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   primary key (id)
);

alter table ai_application comment '应用';

/*==============================================================*/
/* Table: ai_chat_comment                                       */
/*==============================================================*/
create table ai_chat_comment
(
   id                   bigint not null  comment '主键',
   conversation_id      bigint  comment '会话ID',
   content_id           bigint  comment '会话内容',
   user_id              bigint not null default 0  comment '用户ID',
   model_type           varchar(16) not null default ' '  comment '模型类型',
   is_like              smallint not null default 1  comment '是否喜欢，默认喜欢1',
   default_comment      varchar(100) not null  comment '默认评论',
   is_accept            smallint not null default 0  comment '是否采纳，默认否0',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   is_deleted           int not null default 0  comment '是否删除',
   status               int not null default 0  comment '状态',
   primary key (id)
);

alter table ai_chat_comment comment 'AI用户评价结果';

/*==============================================================*/
/* Table: ai_chat_content                                       */
/*==============================================================*/
create table ai_chat_content
(
   id                   bigint not null  comment '主键',
   conversation_id      bigint  comment '会话ID',
   app_id               bigint  comment '应用ID',
   user_id              bigint not null default 0  comment '用户ID',
   model_type           varchar(16) not null default ' '  comment '模型类型',
   command_type         smallint not null default 0  comment '命令类型',
   tools_command        smallint not null default 0  comment '工具指令',
   in_content           text  comment '用户输入',
   in_content_time      datetime  comment '用户输入时间',
   is_command           char(1) not null default '0'  comment '是否命令',
   command_content      varchar(500) not null default ' '  comment '命令的具体内容',
   user_confirmation    char(1) not null default '0'  comment '用户的确认状态',
   confirmation_time    datetime  comment '确认命令的时间',
   out_content_time     datetime  comment '模型输出数据',
   out_content          text  comment '模型输出',
   out_content_type     smallint not null default 0  comment '输出文本类型0 普通文本|1富文本',
   model_text_size      int not null default 0  comment '大模型总输出字数',
   model_input_tokens   int not null default 0  comment '大模型输入token数',
   model_output_tokens  int not null default 0  comment '大模型输出token数',
   prompt               varchar(2048) not null default ' '  comment '提示词',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   is_deleted           int not null default 0  comment '是否删除',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   status               int not null default 0  comment '状态',
   primary key (id)
);

alter table ai_chat_content comment 'AI会话内容表';

/*==============================================================*/
/* Table: ai_chat_conversation                                  */
/*==============================================================*/
create table ai_chat_conversation
(
   id                   bigint not null  comment '主键',
   app_id               bigint  comment '应用ID',
   user_id              bigint not null default 0  comment '用户ID',
   title                varchar(2000) not null default ' '  comment '标题',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   is_deleted           int not null default 0  comment '是否删除',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   status               int not null default 0  comment '状态',
   primary key (id)
);

alter table ai_chat_conversation comment 'AI会话信息表';

/*==============================================================*/
/* Table: ai_conversation_meta                                  */
/*==============================================================*/
create table ai_conversation_meta
(
   id                   bigint not null  comment '主键',
   conversation_id      bigint  comment '会话ID',
   meta_key             varchar(50) not null default ' '  comment '键',
   meta_value           varchar(200) not null default ' '  comment '键值',
   user_id              bigint not null default 0  comment '用户ID',
   status               int not null default 0  comment '状态',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   is_deleted           int not null default 0  comment '是否删除',
   remark               varchar(100) not null default ' '  comment '备注',
   sort                 int not null default 0  comment '排序',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   primary key (id)
);

alter table ai_conversation_meta comment '对话元数据表';

/*==============================================================*/
/* Table: ai_memory_access_log                                  */
/*==============================================================*/
create table ai_memory_access_log
(
   id                   bigint not null  comment '主键',
   agent_memory_id      bigint  comment '记忆实体',
   access_time          datetime  comment '访问时间',
   access_type          smallint  comment '访问类型-1-READ|2-UPDATE|3-DELETE',
   accessed_by          bigint  comment '访问者',
   query_context        text  comment '查询内容',
   metadata             text  comment '元数据-JSON格式',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   is_deleted           int not null default 0  comment '是否删除',
   status               int not null default 0  comment '状态',
   primary key (id),
   key AK_idx_memory_access_log_access_time (access_time)
);

alter table ai_memory_access_log comment '记忆访问记录表';

/*==============================================================*/
/* Table: ai_memory_category                                    */
/*==============================================================*/
create table ai_memory_category
(
   id                   bigint not null  comment '主键',
   category_name        varchar(100)  comment '分类名称',
   parent_id            bigint  comment '上一级',
   description          varchar(1000)  comment '描述',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   is_deleted           int not null default 0  comment '是否删除',
   status               int not null default 0  comment '状态',
   primary key (id)
);

alter table ai_memory_category comment '记忆分类';

/*==============================================================*/
/* Table: ai_menu                                               */
/*==============================================================*/
create table ai_menu
(
   id                   bigint not null  comment '主键',
   app_id               bigint  comment '应用ID',
   name                 varchar(100) not null default ' '  comment '名称',
   labels               varchar(100) not null default ' '  comment '标签',
   icon                 varchar(100) not null default ' '  comment '图标',
   code                 varchar(32) not null default ' '  comment '编码',
   description          varchar(200) not null default ' '  comment '描述',
   source               varchar(20) not null default ' '  comment '菜单资源',
   category             varchar(1) not null default ' '  comment '菜单类型',
   action               varchar(1) not null default ' '  comment '按钮类型',
   is_open              varchar(1) not null default ' '  comment '是否打开新页面',
   path                 varchar(255) not null default ' '  comment '请求地址',
   request_type         smallint not null default 0  comment '请求类型-0内部|1外部',
   page_id              bigint not null default 0  comment '菜单页面id',
   parent_id            bigint not null default 0  comment '父级ID',
   level                int not null default 0  comment '层级',
   sort                 int not null default 0  comment '排序',
   tenant_id            bigint not null default 0  comment '租户',
   status               int not null default 0  comment '状态',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   is_deleted           int not null default 0  comment '是否删除',
   remark               varchar(100) not null default ' '  comment '备注',
   menu_version         int  comment '菜单版本',
   version              int not null default 0  comment '版本',
   primary key (id)
);

alter table ai_menu comment '菜单';

/*==============================================================*/
/* Table: ai_prompt_template                                    */
/*==============================================================*/
create table ai_prompt_template
(
   id                   bigint not null  comment '主键',
   tpl_name             varchar(100)  comment '模版名称',
   tpl_description      varchar(500)  comment '模版描述',
   content              text  comment '内容',
   author               bigint  comment '作者',
   metadata             text  comment '元数据',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   is_deleted           int not null default 0  comment '是否删除',
   status               int not null default 0  comment '状态',
   parent_id            bigint  comment '上一级',
   primary key (id)
);

alter table ai_prompt_template comment '提示词模版';

/*==============================================================*/
/* Table: ai_prompt_variable                                    */
/*==============================================================*/
create table ai_prompt_variable
(
   id                   bigint not null  comment '主键',
   prompt_template_id   bigint  comment '提示词模版',
   name                 varchar(100)  comment '名称',
   type                 varchar(20)  comment '类型',
   description          varchar(1000)  comment '描述',
   default_value        text  comment '默认值',
   is_required          smallint  comment '是否必填',
   allowed_values       text  comment '允许值-JSON格式',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   is_deleted           int not null default 0  comment '是否删除',
   status               int not null default 0  comment '状态',
   primary key (id)
);

alter table ai_prompt_variable comment '提示词变量';

/*==============================================================*/
/* Table: ai_task                                               */
/*==============================================================*/
create table ai_task
(
   id                   bigint not null  comment '主键',
   parent_id            bigint  comment '上一级任务',
   chat_content_id      bigint  comment '会话内容',
   name                 varchar(100) not null default ' '  comment '名称',
   description          varchar(200) not null default ' '  comment '描述',
   task_type            smallint not null default 0  comment '任务类型0同步任务|1异步任务，默认0',
   task_status          smallint not null default 1  comment '任务状态-1待处理|2处理中|3成功|4失败|5已过期，默认0',
   action_type          smallint not null default 0  comment '任务执行方式-0串行|1并发，默认0',
   priority             smallint not null default 0  comment '优先级，数字越大优先级越高【0,，99】默认0',
   result_code          varchar(32) not null default '0'  comment '返回码',
   result_message       varchar(255) not null default ' '  comment '返回消息',
   expire_time          datetime not null  comment '过期时间',
   execute_id           varchar(100) not null default ' '  comment '执行ID',
   execute_count        int not null default 0  comment '执行次数',
   business_param       varchar(2048)  comment '业务参数',
   response_param       varchar(2048)  comment '响应参数',
   ext_info             varchar(100)  comment '扩展信息',
   status               int not null default 0  comment '状态',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   is_deleted           int not null default 0  comment '是否删除',
   remark               varchar(100) not null default ' '  comment '备注',
   sort                 int default 0  comment '排序',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   intent_code          varchar(20)  comment '意图编码',
   primary key (id)
);

alter table ai_task comment 'AI任务表';

/*==============================================================*/
/* Table: ai_tools                                              */
/*==============================================================*/
create table ai_tools
(
   id                   bigint not null  comment '主键',
   name                 varchar(100) not null default ' '  comment '名称',
   tool_type            char(1) not null default '0'  comment '类型',
   description          varchar(200) not null default ' '  comment '描述',
   status               int not null default 0  comment '状态',
   create_user          bigint not null default 0  comment '创建人',
   update_user          bigint not null default 0  comment '更新人',
   create_time          datetime  comment '创建时间',
   update_time          datetime not null default CURRENT_TIMESTAMP  comment '更新时间',
   is_deleted           int not null default 0  comment '是否删除',
   remark               varchar(100) not null default ' '  comment '备注',
   sort                 int default 0  comment '排序',
   tenant_id            bigint not null default 0  comment '租户',
   version              int not null default 0  comment '版本',
   command              smallint  comment '工具指令',
   primary key (id)
);

alter table ai_tools comment '工具';

