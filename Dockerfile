FROM hb.richinfo.devops/library/base-java:17-jdk

MAINTAINER <EMAIL>

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN mkdir -p /richinfo

WORKDIR /richinfo

EXPOSE 9302

ADD ./target/rc-platform-devagent.jar ./app.jar

ENV PARAMS=""

ENTRYPOINT ["java", "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED","-DJASYPT_ENCRYPTKEY=richok@123", "-Djava.security.egd=file:/dev/./urandom $PARAMS", "-jar", "app.jar"]

CMD ["--spring.profiles.active=<env>"]
